#!/usr/bin/env node

/**
 * Memory optimization script
 * Chạy script n<PERSON><PERSON> để analyze và optimize memory usage
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Starting memory optimization analysis...\n');

// 1. Analyze bundle size
function analyzeBundleSize() {
  console.log('📦 Analyzing bundle size...');
  
  const nextDir = path.join(process.cwd(), '.next');
  if (!fs.existsSync(nextDir)) {
    console.log('❌ .next directory not found. Run "yarn build" first.');
    return;
  }

  // Check for large files
  const staticDir = path.join(nextDir, 'static');
  if (fs.existsSync(staticDir)) {
    const files = getAllFiles(staticDir);
    const largeFiles = files
      .map(file => ({
        path: file,
        size: fs.statSync(file).size
      }))
      .filter(file => file.size > 500 * 1024) // Files > 500KB
      .sort((a, b) => b.size - a.size);

    if (largeFiles.length > 0) {
      console.log('⚠️  Large files detected:');
      largeFiles.forEach(file => {
        console.log(`   ${formatFileSize(file.size)} - ${path.relative(process.cwd(), file.path)}`);
      });
    } else {
      console.log('✅ No large files detected');
    }
  }
}

// 2. Check for memory leaks in code
function checkMemoryLeaks() {
  console.log('\n🔍 Checking for potential memory leaks...');
  
  const srcDir = path.join(process.cwd(), 'src');
  const files = getAllFiles(srcDir, ['.tsx', '.ts', '.js', '.jsx']);
  
  const issues = [];
  
  files.forEach(file => {
    const content = fs.readFileSync(file, 'utf8');
    const relativePath = path.relative(process.cwd(), file);
    
    // Check for common memory leak patterns
    const patterns = [
      {
        regex: /setInterval\s*\(/g,
        message: 'setInterval without cleanup',
        severity: 'warning'
      },
      {
        regex: /addEventListener\s*\(/g,
        message: 'addEventListener without removeEventListener',
        severity: 'warning'
      },
      {
        regex: /new\s+Array\s*\(\s*\d{4,}\s*\)/g,
        message: 'Large array allocation',
        severity: 'error'
      },
      {
        regex: /\.map\s*\([^)]*\)\s*\.map\s*\(/g,
        message: 'Chained map operations (consider optimization)',
        severity: 'info'
      },
      {
        regex: /console\.log\s*\(/g,
        message: 'console.log in production code',
        severity: 'info'
      }
    ];
    
    patterns.forEach(pattern => {
      const matches = content.match(pattern.regex);
      if (matches) {
        issues.push({
          file: relativePath,
          message: pattern.message,
          severity: pattern.severity,
          count: matches.length
        });
      }
    });
  });
  
  if (issues.length > 0) {
    console.log('⚠️  Potential issues found:');
    issues.forEach(issue => {
      const icon = issue.severity === 'error' ? '🔴' : issue.severity === 'warning' ? '🟡' : 'ℹ️';
      console.log(`   ${icon} ${issue.file}: ${issue.message} (${issue.count} occurrences)`);
    });
  } else {
    console.log('✅ No obvious memory leak patterns detected');
  }
}

// 3. Analyze dependencies
function analyzeDependencies() {
  console.log('\n📋 Analyzing dependencies...');
  
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.log('❌ package.json not found');
    return;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  // Check for heavy dependencies
  const heavyDeps = [
    'lodash', 'moment', 'jquery', 'bootstrap', 'material-ui',
    'antd', 'semantic-ui', 'bulma'
  ];
  
  const foundHeavyDeps = Object.keys(deps).filter(dep => 
    heavyDeps.some(heavy => dep.includes(heavy))
  );
  
  if (foundHeavyDeps.length > 0) {
    console.log('⚠️  Heavy dependencies detected:');
    foundHeavyDeps.forEach(dep => {
      console.log(`   📦 ${dep}: ${deps[dep]}`);
    });
    console.log('   💡 Consider using lighter alternatives or tree-shaking');
  } else {
    console.log('✅ No heavy dependencies detected');
  }
  
  // Check for duplicate functionality
  const duplicates = [
    ['axios', 'fetch', 'node-fetch'],
    ['lodash', 'ramda', 'underscore'],
    ['moment', 'dayjs', 'date-fns'],
    ['styled-components', 'emotion', 'stitches']
  ];
  
  duplicates.forEach(group => {
    const found = group.filter(dep => deps[dep]);
    if (found.length > 1) {
      console.log(`⚠️  Duplicate functionality: ${found.join(', ')}`);
    }
  });
}

// 4. Generate optimization recommendations
function generateRecommendations() {
  console.log('\n💡 Optimization Recommendations:');
  
  const recommendations = [
    '1. Enable React Strict Mode in next.config.ts',
    '2. Use dynamic imports for large components',
    '3. Implement proper cleanup in useEffect hooks',
    '4. Use React.memo for expensive components',
    '5. Optimize images with next/image',
    '6. Enable compression in production',
    '7. Use SWR or React Query for data caching',
    '8. Implement virtual scrolling for large lists',
    '9. Use Web Workers for heavy computations',
    '10. Monitor memory usage with performance.measureUserAgentSpecificMemory()'
  ];
  
  recommendations.forEach(rec => console.log(`   ✨ ${rec}`));
}

// Utility functions
function getAllFiles(dir, extensions = []) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile()) {
        if (extensions.length === 0 || extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    });
  }
  
  if (fs.existsSync(dir)) {
    traverse(dir);
  }
  
  return files;
}

function formatFileSize(bytes) {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

// Run analysis
analyzeBundleSize();
checkMemoryLeaks();
analyzeDependencies();
generateRecommendations();

console.log('\n🎉 Memory optimization analysis complete!');
console.log('💡 Run this script after making changes to track improvements.');
