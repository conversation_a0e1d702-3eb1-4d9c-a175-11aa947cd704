import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  /* config options here */
  output: 'standalone',
  reactStrictMode: false,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'storage.big360.ai',
        port: '',
        pathname: '/**'
      },
    ]
  },
  env: {
    MESSENGER_URL: process.env.NEXT_PUBLIC_MESSENGER_URL,
    LANDING_HOST: process.env.NEXT_PUBLIC_LANDING_HOST,
    APP_DOMAIN: process.env.NEXT_PUBLIC_APP_DOMAIN,
    API_URL: process.env.NEXT_PUBLIC_API_URL,
    X_TOKEN: process.env.NEXT_PUBLIC_TOKEN
  }
};

export default nextConfig;
