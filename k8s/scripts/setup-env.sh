#!/bin/bash
set -o errexit
set -o pipefail
set -o nounset

echo HELLO="hello-world" >> .env

NEXT_PUBLIC_MESSENGER_URL=$(eval echo "\${NEXT_PUBLIC_MESSENGER_URL}")
NEXT_PUBLIC_LANDING_HOST=$(eval echo "\${NEXT_PUBLIC_LANDING_HOST}")
NEXT_PUBLIC_APP_DOMAIN=$(eval echo "\${NEXT_PUBLIC_APP_DOMAIN}")
NEXT_PUBLIC_API_URL=$(eval echo "\${NEXT_PUBLIC_API_URL}")
NEXT_PUBLIC_TOKEN=$(eval echo "\${NEXT_PUBLIC_TOKEN}")

# Write to .env.prod
{
    echo "NEXT_PUBLIC_MESSENGER_URL=${NEXT_PUBLIC_MESSENGER_URL}"
    echo "NEXT_PUBLIC_LANDING_HOST=${NEXT_PUBLIC_LANDING_HOST}"
    echo "NEXT_PUBLIC_APP_DOMAIN=${NEXT_PUBLIC_APP_DOMAIN}"
    echo "NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}"
    echo "NEXT_PUBLIC_TOKEN=${NEXT_PUBLIC_TOKEN}"
} >> .env.prod

exit 0;
