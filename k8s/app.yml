# Deployment for Next.js application
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldp-big360-app
  namespace: ${NAMESPACE}  # Replace with your namespace
  labels:
    app: ldp-big360-app
spec:
  replicas: ${REPLICAS}  # Replace with the desired number of replicas
  selector:
    matchLabels:
      app: ldp-big360-app
  template:
    metadata:
      labels:
        app: ldp-big360-app
    spec:
      containers:
      - name: ldp-big360
        image: ${IMAGE_TAG}  # Replace with your image
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3000
          name: http
        resources:
          limits:
            cpu: "2"
            memory: "3Gi"
          requests:
            cpu: "1"
            memory: "2Gi"
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 15
        envFrom:
          - secretRef:
              name: ldp-big360-environment
      imagePullSecrets:
        - name: registry-skylink  # Replace with your image pull secret if needed
      restartPolicy: Always
---
# Service to expose the Next.js application
apiVersion: v1
kind: Service
metadata:
  name: ldp-big360-service
  namespace: ${NAMESPACE}  # Replace with your namespace
  labels:
    app: ldp-big360-app
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: ldp-big360-app
