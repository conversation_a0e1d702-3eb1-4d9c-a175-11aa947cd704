/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    screens: {
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1400px",
      "3xl": "2560px",
    },
    fontFamily: {
      inter: ["Inter", "sans-serif"],
      sans: ["sans-serif"],
    },
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      backgroundColor: {
        primary: "hsl(var(--background-primary))",
        secondary: "hsl(var(--background-secondary))",
        card: "hsl(var(--card))",
        active: "hsl(var(--background-active))",
        tertiary: "#E1E2E3",
        hover: "#383C4A",
        "primary-hover": "hsl(var(--background-primary-hover))",
        "error-subtitle": "#FFD9D9",
        "error-default": "#F53E3E",

        brand: {
          disabled: "#E2DAFF",
          subtitle: "#E2DAFF",
        },
        success: {
          primary: "#E0F8E3",
        },

        custom: {
          primary: "#FDFDFD",
          secondary: "#F0F0F0",
          tertiary: "#6B7183",
          disable: "#E1E2E3",
        },
      },
      textColor: {
        primary: "hsl(var(--text-primary))",
        secondary: "hsl(var(--text-secondary))",
        tertiary: "hsl(var(--text-tertiary))",
        custom: {
          disable: "#A7AAB1",
          brand: "#FDFDFD",
          tertiary: "#6B7183",
        },
        success: {
          default: "#27923A",
          subtitle: "#205B2B",
        },
      },
      colors: {
        "primary-hover": "hsl(var(--background-primary-hover))",
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: "hsl(var(--background-primary))",
        secondary: "hsl(var(--background-secondary))",
        tertiary: "#6B7183",
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        icon: {
          primary: "hsl(var(--text-secondary))",
        },
        shadow: {
          light: "hsl(var(--color-shadow-light))",
          medium: "hsl(var(--color-shadow-medium))",
        },
        infor: {
          primary: "hsl(208, 100%, 59%, 1)",
          disable: "#a7aab1",
          subtitle: "#EEF9FF",
          text: "#146BE1",
        },
        warning: {
          default: "#FBCA24",
          "subtitle-default": "#FFFCEB",
          "text-strong": "#D98206",
        },
        error: {
          default: "#F53E3E",
          subtitle: "#FFD9D9",
          strong: "#BF1616",
          light: "#FFA2A2",
        },
        brand: {
          strong: "#5A18BF",
          text: "#FDFDFD",
          default: "#8F5CFF",
        },
      },
      backgroundImage: {
        primary: {
          DEFAULT: "var(--primary-gradients)",
        },
        "custom-gradient":
          "linear-gradient(304.96deg, #A33AD8 -3.24%, #8347CE 48.97%, #5138B9 102.28%)",
      },
      borderColor: {
        normal: "hsl(var(--border))",
        focus: "hsl(var(--border-focus))",
        primary: "hsl(var(--border-primary))",
        secondary: "hsl(var(--border-secondary))",
        tertiary: "hsl(var(--border-tertiary))",
        inverse: "hsl(var(--border-inverse))",
        disable: "hsl(var(--border-disable))",
        error: "#F53E3E",
        default: "#FDFDFD",
        custom: {
          primary: "#E1E2E3",
          success: "#205B2B",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "8px",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "spin-slow": {
          from: { transform: "rotate(0deg)" },
          to: { transform: "rotate(360deg)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "spin-slow": "spin-slow 5s linear infinite",
      },
      fontSize: {
        xl: ["var(--text-xl)", { lineHeight: "var(--text-xl-line-height)" }],
        lg: ["var(--text-lg)", { lineHeight: "var(--text-lg-line-height)" }],
        md: ["var(--text-md)", { lineHeight: "var(--text-md-line-height)" }],
        sm: ["var(--text-sm)", { lineHeight: "var(--text-sm-line-height)" }],
        xs: ["var(--text-xs)", { lineHeight: "var(--text-xs-line-height)" }],
        "dp-2xl": [
          "var(--dp-2xl)",
          {
            lineHeight: "var(--dp-2xl-line-height)",
            letterSpacing: "var(--dp-2xl-letter-spacing)",
          },
        ],
        "dp-xl": [
          "var(--dp-xl)",
          { lineHeight: "var(--dp-xl-line-height)", letterSpacing: "var(--dp-xl-letter-spacing)" },
        ],
        "dp-lg": [
          "var(--dp-lg)",
          { lineHeight: "var(--dp-lg-line-height)", letterSpacing: "var(--dp-lg-letter-spacing)" },
        ],
        "dp-md": [
          "var(--dp-md)",
          { lineHeight: "var(--dp-md-line-height)", letterSpacing: "var(--dp-md-letter-spacing)" },
        ],
        "dp-sm": ["var(--dp-sm)", { lineHeight: "var(--dp-sm-line-height)" }],
        "dp-xs": ["var(--dp-xs)", { lineHeight: "var(--dp-xs-line-height)" }],
      },
      boxShadow: {
        xs: "0px 1px 2px 0px #14151A0D",
        focus: "var(--shadow-focus)",
        chart:
          "rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px",
        btn: "0px 1px 2px 0px #14151A0D",
        toggle: "0px 1px 2px 0px hsla(210, 13%, 62%, 0.16)",
        box: "0px 12px 24px -4px #919EAB1F, 0px 0px 2px 0px #919EAB33",
        // sm: "0px 3px 10px -2px #14151A05, 0px 10px 16px -3px #14151A0D",
        sm: "0px 0px 4px -4px var(--color-shadow-light), 0px 1px 4px 0px var(--color-shadow-medium)",
        medium: "0px 0px 32px 0px rgba(9, 10, 13, 0.05), 0px 4px 20px -8px rgba(9, 10, 13, 0.15)",
        lg: " 0px 0px 32px 0px rgba(9, 10, 13, 0.10), 0px 20px 24px -24px  rgba(9, 10, 13, 0.02)",
      },
    },
  },
};
