# Dependencies
node_modules
.pnp
.pnp.js
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# Testing
/coverage
__tests__
*.test.js
*.spec.js

# Next.js
.next/
out/

# Production
/build

# Misc
.DS_Store
*.pem
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editors
.idea/
.vscode/
*.swp
*.swo

# Version control
.git
.gitignore

# Project files
README.md
CHANGELOG.md
docker-compose.yml
docker-compose.*.yml
Dockerfile*
.dockerignore

# Logs
logs
*.log

# Kubernetes
k8s/
*.yaml
*.yml
!next.config.js
!next.config.ts
!tailwind.config.js
