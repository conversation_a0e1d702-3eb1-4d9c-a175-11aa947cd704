{"name": "landingpage-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint"}, "dependencies": {"@ag-grid-community/client-side-row-model": "^32.3.4", "@next/env": "^15.2.1", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@remixicon/react": "^4.2.0", "@tanstack/react-table": "^8.21.2", "@types/lodash": "^4.17.16", "ag-grid-community": "^33.1.1", "ag-grid-react": "^33.1.1", "axios": "^1.8.4", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash": "^4.17.21", "lucide-react": "^0.483.0", "next": "15.2.1", "next-sitemap": "^4.2.3", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-dom": "^19.0.0", "remixicon-react": "^1.0.0", "styled-components": "^6.1.16", "swiper": "^11.2.5", "swr": "^2.3.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "typewriter-effect": "^2.21.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.1", "tailwindcss": "^4.0.12", "typescript": "^5"}}