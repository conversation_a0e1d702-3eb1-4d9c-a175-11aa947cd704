stages:
  - test
  - build
  - deploy

variables:
  DEV_IMAGE_TAG: ${DOCKER_REGISTRY_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/dev:$CI_COMMIT_SHORT_SHA
  PROD_IMAGE_TAG: ${DOCKER_REGISTRY_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/prod:$CI_COMMIT_SHORT_SHA

# Test stage
test:
  stage: test
  image: alpine:latest
  script:
    - echo "This is test"

# Build stage
.build_template: &build_definition
  stage: build
  script:
    - docker build -f ${DOCKER_FILE} -t ${IMAGE_TAG} .
    - docker images ${IMAGE_TAG}
    - docker push ${IMAGE_TAG}

build-prod:
  <<: *build_definition
  variables:
    IMAGE_TAG: ${PROD_IMAGE_TAG}
    DOCKER_FILE: Dockerfile
  only:
    - main
  when: manual
  tags:
    - runner-01

.deploy_template: &deploy_definition
  stage: deploy
  image:
    name: bitnami/kubectl:1.29
    entrypoint: [""]
  script:
    - echo "Deploying to Kubernetes"
    - bash ./k8s/scripts/setup-env.sh
    - kubectl delete secret ldp-big360-environment --ignore-not-found --namespace=${NAMESPACE}
    - kubectl create secret generic ldp-big360-environment --from-env-file=.env.prod --namespace=${NAMESPACE} --dry-run=client -o yaml | kubectl apply -f -
    - envsubst < k8s/app.yml | kubectl apply -f -
    # Wait for deployment to complete
    - kubectl rollout status deployment/ldp-big360-app -n $NAMESPACE --timeout=300s

deploy-prod:
  <<: *deploy_definition
  variables:
    IMAGE_TAG: ${PROD_IMAGE_TAG}
    NAMESPACE: ${PROD_NAMESPACE}
    REPLICAS: 2
  only:
    - main
  needs:
    - build-prod
  when: manual
  tags:
    - ci-dev
