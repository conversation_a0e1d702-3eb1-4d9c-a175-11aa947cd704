interface Props {
  width?: number;
  height?: number;
  color?: string;
}

const IconFavicon = ({ width, height, color }: Props) => {
  return (
    <svg
      width={width || "30"}
      height={height || "30"}
      viewBox="0 0 31 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.6794 0.653564C24.0889 0.653564 30.9061 7.52438 30.9061 16C30.9061 24.4755 24.0889 31.3464 15.6794 31.3464C7.26988 31.3464 0.452637 24.4755 0.452637 16C0.452637 7.52438 7.26988 0.653564 15.6794 0.653564ZM16.3691 9.16641C16.8261 9.62511 16.8261 10.3668 16.3691 10.8237C15.9122 11.2805 15.1689 11.2805 14.7119 10.8237C14.255 10.365 14.255 9.62327 14.7119 9.16641C15.1689 8.70955 15.9122 8.70955 16.3691 9.16641ZM7.5401 16.848C7.26298 16.4429 7.0189 16.2857 6.6904 16.2857H6.68856C6.23527 16.2857 5.89942 16.6298 5.89942 17.1033C5.89942 18.3425 7.441 19.4837 9.45239 19.4837C11.695 19.4837 13.2733 18.2297 13.2733 16.4356C13.2733 15.1371 12.3428 14.0717 11.1059 13.9515V13.8239C12.1336 13.6741 12.9595 12.6531 12.9595 11.5414C12.9595 9.94334 11.5152 8.82432 9.45973 8.82432C7.51441 8.82432 6.06276 9.92855 6.06276 11.129C6.06276 11.6154 6.40411 11.9539 6.88126 11.9539C7.23179 11.9539 7.49239 11.7967 7.71628 11.4212C8.11085 10.7461 8.68344 10.4002 9.42119 10.4002C10.3682 10.4002 11.0233 10.9643 11.0233 11.7893C11.0233 12.6142 10.3535 13.2153 9.43771 13.2153H8.74583C8.29254 13.2153 7.97138 13.539 7.97138 14.0033C7.97138 14.4675 8.29988 14.806 8.74583 14.806H9.47625C10.57 14.806 11.3078 15.4367 11.3078 16.3671C11.3078 17.2975 10.5865 17.906 9.46891 17.906C8.63572 17.906 7.97137 17.5379 7.5401 16.848ZM18.1183 20.303C18.1183 20.5009 18.1623 20.6989 18.2798 20.8968L18.2808 20.8947C18.786 21.7225 19.9821 22.3284 21.5831 22.3358C24.1102 22.3506 25.5894 21.1206 25.5894 19.0786V12.4624C25.5894 11.7374 25.1416 11.3786 24.5268 11.3786C23.912 11.3786 23.4495 11.7392 23.4495 12.4624V12.8934H23.4073C22.9981 12.0093 22.1429 11.4156 21.0142 11.4156C18.9918 11.4156 17.7916 12.936 17.7916 15.3312C17.7916 17.7265 19.023 19.2192 20.9775 19.2192C22.1135 19.2192 23.0274 18.6587 23.3945 17.8153H23.4367V19.0675C23.4367 20.0478 22.7485 20.6915 21.5776 20.6841C20.8674 20.6785 20.3095 20.377 19.7314 19.8277C19.4873 19.6094 19.2689 19.5206 18.99 19.5206C18.4541 19.5206 18.1183 19.8554 18.1183 20.303ZM21.6987 13.0414C22.7687 13.0414 23.4569 13.9514 23.4569 15.3627C23.4569 16.7813 22.7613 17.6322 21.6987 17.6322C20.6362 17.6322 19.9773 16.7758 19.9773 15.3553C19.9773 13.9348 20.6288 13.0414 21.6987 13.0414ZM14.3616 19.4837V14.2973C14.3616 12.9951 15.4168 11.9409 16.7198 11.9409V17.1272C16.7198 18.4294 15.6646 19.4837 14.3616 19.4837Z"
        fill={color || "#924FE8"}
      />
    </svg>
  );
};

export default IconFavicon;
