type Props = {
  color?: string;
  size?: number;
};

const WomanIcon: React.FC<Props> = ({ color = "white", size = 14 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1048_44808)">
        <path
          d="M6.41667 9.29482C5.31763 9.14582 4.31618 8.58502 3.61483 7.72583C2.91349 6.86664 2.56459 5.77317 2.63869 4.66656C2.71279 3.55994 3.20436 2.52275 4.014 1.76474C4.82363 1.00673 5.89091 0.584453 7.00001 0.583315C8.11046 0.582245 9.17977 1.00347 9.99115 1.76161C10.8025 2.51974 11.2953 3.55804 11.3694 4.66601C11.4436 5.77398 11.0937 6.86871 10.3907 7.72825C9.68762 8.58779 8.68402 9.14783 7.58334 9.29482V10.5H10.5V11.6666H7.58334V14H6.41667V11.6666H3.50001V10.5H6.41667V9.29482ZM7.00001 8.16665C7.85091 8.16665 8.66696 7.82863 9.26864 7.22695C9.87032 6.62527 10.2083 5.80922 10.2083 4.95831C10.2083 4.10741 9.87032 3.29136 9.26864 2.68968C8.66696 2.088 7.85091 1.74998 7.00001 1.74998C6.1491 1.74998 5.33305 2.088 4.73137 2.68968C4.12969 3.29136 3.79167 4.10741 3.79167 4.95831C3.79167 5.80922 4.12969 6.62527 4.73137 7.22695C5.33305 7.82863 6.1491 8.16665 7.00001 8.16665Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_1048_44808">
          <rect width="14" height="14" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default WomanIcon;
