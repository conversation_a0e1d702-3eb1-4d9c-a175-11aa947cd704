type Props = {
  color?: string;
  size?: string;
};

const ViewAllIcon: React.FC<Props> = ({ color = "#0F1324", size = "14" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.99956 1.75C10.1449 1.75 12.7617 4.01333 13.3106 7C12.7623 9.98667 10.1449 12.25 6.99956 12.25C3.85423 12.25 1.23739 9.98667 0.688477 7C1.23681 4.01333 3.85423 1.75 6.99956 1.75ZM6.99956 11.0833C8.18925 11.0831 9.34362 10.679 10.2737 9.93718C11.2038 9.19538 11.8546 8.15983 12.1195 7C11.8536 5.84109 11.2024 4.80667 10.2724 4.06585C9.34241 3.32503 8.18857 2.92164 6.99956 2.92164C5.81055 2.92164 4.65671 3.32503 3.7267 4.06585C2.79668 4.80667 2.14551 5.84109 1.87964 7C2.14453 8.15983 2.79529 9.19538 3.72539 9.93718C4.6555 10.679 5.80987 11.0831 6.99956 11.0833ZM6.99956 9.625C6.30337 9.625 5.63569 9.34844 5.14341 8.85615C4.65112 8.36387 4.37456 7.69619 4.37456 7C4.37456 6.30381 4.65112 5.63613 5.14341 5.14384C5.63569 4.65156 6.30337 4.375 6.99956 4.375C7.69575 4.375 8.36343 4.65156 8.85572 5.14384C9.348 5.63613 9.62456 6.30381 9.62456 7C9.62456 7.69619 9.348 8.36387 8.85572 8.85615C8.36343 9.34844 7.69575 9.625 6.99956 9.625ZM6.99956 8.45833C7.38633 8.45833 7.75727 8.30469 8.03076 8.0312C8.30425 7.75771 8.45789 7.38677 8.45789 7C8.45789 6.61323 8.30425 6.24229 8.03076 5.9688C7.75727 5.69531 7.38633 5.54167 6.99956 5.54167C6.61279 5.54167 6.24185 5.69531 5.96836 5.9688C5.69487 6.24229 5.54123 6.61323 5.54123 7C5.54123 7.38677 5.69487 7.75771 5.96836 8.0312C6.24185 8.30469 6.61279 8.45833 6.99956 8.45833Z"
        fill={color}
        fillOpacity="0.6"
      />
    </svg>
  );
};

export default ViewAllIcon;
