import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // Chỉ log trong development để giảm overhead
  if (process.env.NODE_ENV === 'development') {
    const ip = request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
               request.headers.get('x-real-ip') ||
               'Unknown';
    const method = request.method;
    const pathname = request.nextUrl.pathname;

    // Simplified timestamp
    const timestamp = new Date().toISOString().slice(11, 19); // HH:mm:ss format

    console.log(`🧭 [${timestamp}] ${method} ${pathname} from ${ip}`);
  }

  return NextResponse.next();
}

export const config = {
  // Optimized matcher để exclude nhiều static files hơn
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|ico|css|js)$).*)',
  ],
};
