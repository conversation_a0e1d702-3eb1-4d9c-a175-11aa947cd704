import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const ip = request.headers.get('x-forwarded-for') || 'Unknown IP';
  const method = request.method;
  const pathname = request.nextUrl.pathname;

  const timestamp = new Date().toLocaleString('en-US', { timeZone: 'UTC' });

  console.log(`🧭 [${timestamp}] ${method} ${pathname} from ${ip}`);

  return NextResponse.next();
}

export const config = {
  matcher: '/((?!_next/static|_next/image|favicon.ico).*)',
};
