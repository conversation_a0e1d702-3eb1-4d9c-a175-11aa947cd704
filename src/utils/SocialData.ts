import { AgeGroup, CityGroup, GenderGroup, Overview } from "./ResponseApi";
import { TYPE_SOCIAL } from '@/utils/constant';

export enum SUB_TYPE_SOCIAL {
  "private_group" = 1,
  "public_group" = 2,
  "ad_post" = 3,
  "live_post" = 4,
}

export interface IUserPreview {
  phone: string;
  fullname: string;
  fb_uid: string;
  email: null;
  username: null;
  gender: string;
  dob: string;
  city: string;
  location: string;
  relationships: string;
  scores: {
    category: number;
  };
}
export interface Summarize {
  age_gender: AgeGroup;
  city: CityGroup;
  gender: GenderGroup;
  overview: Overview;
  relationships: Record<string, number | undefined>;
}
export interface TSocialDetail {
  id: string;
  fb_uid: string;
  name: string;
  description: string;
  type: TYPE_SOCIAL;
  category: string;
  size: number;
  rating: number;
  package: string;
  date_created: string;
  date_updated: string;
  data_release: string;
  datatype: 'AUDIENCE' | 'DATASET';
  summarize: Summarize;
  latest_audience: number;
  actor_id: string;
  subtype: SUB_TYPE_SOCIAL;
  attachments?: string[];
  is_aud_added?: boolean;
  is_ds_added?: boolean;
  system?: boolean;
}


export interface TDataSummarize<T> {
  data: T;
  loading: boolean;
}

export interface TDataPreview {
  data: {
    items: IUserPreview[],
    count: number;
  };
  loading: boolean;
  total: number;
}
export type SocialPersonaDimType = 'city' | 'relationships' | 'category' | 'category_ranking';

export interface Summarize {
  age_gender: AgeGroup;
  city: CityGroup;
  gender: GenderGroup;
  overview: Overview;
  relationships: Record<string, number | undefined>;
}
