export interface TBaseResponse<T> {
  data: T;
  message?: string;
  status?: number;
  error?: any;
  code: 0 | 1;
  response?: any;
}

export interface AgeGroup {
  [key: string]: {
    F: number;
    M: number;
    NULL: number;
  };
}

export interface CityGroup {
  [key: string]: number;
}
export interface GenderGroup {
  F: number;
  M: number;
  NULL: number;
}
export interface Overview {
  c_dob: number;
  total: number;
  c_city: number;
  c_email: number;
  c_phone: number;
  c_gender: number;
  c_localtion: number;
  c_relationships: number;
}
