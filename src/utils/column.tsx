import { ColDef, ICellRendererParams, ValueFormatterParams } from 'ag-grid-community';
import RatingColumn from '@/components/SocialDataView/columns/RatingColumn';
import TypeColumn from '@/components/SocialDataView/columns/TypeColumn';
import PackageColumn from '@/components/SocialDataView/columns/PackageColumn';
import InformationColumn from '@/components/SocialDataView/columns/InformationColumn';
import AudiencesSizeColumn from '@/components/Column/AudiencesSizeColumn';
import { AudienceCategories, SortType } from '@/utils/constant';
import { ColumnDef } from '@tanstack/table-core';
import { IUserPreview } from '@/utils/SocialData';
import DefaultColumn from '@/components/Column/DefaultColumn';
import GenderColumn from '@/components/Column/GenderColumn';
import { formatDate, formatLabel } from '@/utils/utils';
import ScoresColumn from '@/components/SocialDataDetail/ScoresColumn';
import InfoDetailCol from '@/components/SocialDataView/columns/InfoDetailCol';

export const TRENDING_COLUMN: ColDef[] = [
  {
    field: "name",
    headerName: "Trending",
    width: 250,
    minWidth: 180,
    maxWidth: 438,
    flex: 1,
    cellRenderer: InformationColumn,
  },
  {
    field: "size",
    headerName: "Sizes",
    headerClass:'[&>.ag-header-cell-comp-wrapper]:justify-end [&>.ag-header-cell-comp-wrapper]:pr-4',
    cellRenderer: (params: ICellRendererParams) => AudiencesSizeColumn({ size: params.data.size }),
    cellClass: "justify-end !px-4",
    width: 100,
    minWidth: 100,
    maxWidth: 180,
  },
  {
    field: "type",
    headerName: "Type",
    cellRenderer: TypeColumn,
    cellClass:'items-end ',
    width: 130,
    minWidth: 130,
    maxWidth: 130,
  },
  {
    field: "rating",
    headerName: "Rating",
    headerClass:'custom-ag-header--center [&>.ag-header-cell-comp-wrapper]:justify-end [&>.ag-header-cell-comp-wrapper]:!w-[100px] [&>.ag-header-cell-comp-wrapper]:mx-auto [&>.ag-header-cell-comp-wrapper]:px-3',
    cellRenderer: RatingColumn,
    cellClass: "justify-center",
    width: 180,
    minWidth: 130,
    maxWidth: 180,
  },
  {
    field: "package",
    headerName: "Package",
    width: 180,
    minWidth: 100,
    maxWidth: 180,
    cellRenderer: (params: ValueFormatterParams) => (
      <PackageColumn package_value={params.data.package} />
    ),
  },
];
export const SOCIAL_AUDIENCES_COLUMN = ({
  handleSortChange,
}: {
  handleSortChange: (sortType: SortType, field: string) => void;
}) => [
  {
    field: "name",
    headerName: "People/Fanpage/Group",
    width: 250,
    minWidth: 180,
    maxWidth: 438,
    flex: 1,
    cellRenderer: InformationColumn,
  },
  {
    field: "size",
    headerName: "Sizes",
    headerClass:'[&>.ag-header-cell-comp-wrapper]:justify-end [&>.ag-header-cell-comp-wrapper]:pr-4',
    cellRenderer: (params: ICellRendererParams) => AudiencesSizeColumn({ size: params.data.size }),
    sortable: true,
    headerComponentParams: {
      handleSortChange,
    },
    cellClass: "justify-end",
    width: 100,
    minWidth: 100,
    maxWidth: 180,
  },
  {
    field: "type",
    headerName: "Type",
    cellRenderer: TypeColumn,
    width: 130,
    minWidth: 130,
    maxWidth: 130,
  },
  {
    field: "rating",
    headerName: "Rating",
    headerClass:'custom-ag-header--center [&>.ag-header-cell-comp-wrapper]:justify-end [&>.ag-header-cell-comp-wrapper]:!w-[100px] [&>.ag-header-cell-comp-wrapper]:mx-auto [&>.ag-header-cell-comp-wrapper]:px-3',
    cellRenderer: RatingColumn,
    cellClass: "justify-center",
    sortable: true,
    headerComponentParams: {
      handleSortChange,
    },
    width: 180,
    minWidth: 130,
    maxWidth: 180,
  },
  {
    field: "package",
    headerName: "Package",
    width: 180,
    minWidth: 100,
    maxWidth: 180,
    cellRenderer: (params: ValueFormatterParams) => (
      <PackageColumn package_value={params.data.package} />
    ),
  },
];

export const getSocialAudienceColumns = (): ColumnDef<IUserPreview>[] => [
  {
    accessorKey: "fullname",
    header: "Full Name",
    cell: ({ row }) => (
      <div className="w-[200px]">
        <InfoDetailCol {...row.original} />
      </div>
    ),
    meta: {
      "w-[300px] text-left": "w-[300px] text-left",
    },
  },
  {
    accessorKey: "gender",
    header: "Gender",
    cell: ({ row }) => <GenderColumn value={row.original?.gender} />,
  },
  {
    accessorKey: "dob",
    header: "Date of Birth",
    cell: ({ row }) => (
      <div className="w-[110px]">
        <DefaultColumn value={formatDate(row.original.dob)} />
      </div>
    ),
    size: 150,
    meta: {
      "w-[150px] text-left": "w-[150px] text-left",
    },
  },
  {
    accessorKey: "city",
    header: "City",
    cell: ({ row }) => (
      <div className="w-[150px] capitalize">
        <DefaultColumn value={row.original.city} />
      </div>
    ),
    meta: {
      "w-[150px] text-left": "w-[150px] text-left",
    },
  },
  {
    accessorKey: "scores",
    header: "Interest Score",
    cell: ({ row }) => <ScoresColumn {...row.original} />,
    meta: {
      "!w-[250px] text-left": "!w-[250px] text-left",
    },
  },
  {
    accessorKey: "username",
    header: "Relationship",
    cell: ({ row }) => {
      const newData = formatLabel(row.original?.relationships);
      return (
        <div className="capitalize">
          <DefaultColumn value={newData} />
        </div>
      );
    },
    meta: {
      "text-left": "text-left",
    },
  },
  {
    accessorKey: "email",
    header: "Email",
    cell: ({ row }) => <DefaultColumn value={row.original.email} />,
    meta: {
      "text-left": "text-left",
    },
  },
];
