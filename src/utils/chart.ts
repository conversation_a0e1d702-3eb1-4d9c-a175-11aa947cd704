export const calculateThickness = (value: number[], radius: number) => {
  const total = value.reduce((acc, val) => acc + val, 0);
  const arr = value.map((item) => (item / total) * radius);
  const highestNumber = Math.max(...arr);
  return radius - highestNumber;
};
export const getSuitableY = (y: number, yArray: number[] = [], direction: string) => {
  let result = y;
  yArray.forEach((existedY) => {
    if (existedY - 14 < result && existedY + 14 > result) {
      if (direction === "right") {
        result = existedY + 14;
      } else {
        result = existedY - 14;
      }
    }
  });
  return result;
};

export const getOriginPoints = (source: {x: number, y: number}, center: {x: number, y: number}, l: number) => {
  const a = { x: 0, y: 0 };
  const dx = ( center.x - source.x ) / l;
  const dy = ( center.y - source.y ) / l;
  a.x = center.x + l * dx;
  a.y = center.y + l * dy;
  return a;
};
export const formatTitle = (input: string): string => {
  if (!input || input.trim() === "") {
    return "";
  }
  const inputLength = input.split(" ").length;
  const words = input.trim().split(/\s+/);

  const firstChars = words.map((word) => word.charAt(0)).join("");

  return inputLength > 2 ? firstChars.toUpperCase() : input;
};

export const adjustPercentages = (percentages: number[]): number[] => {
  const sqrtValues = percentages.map(Math.sqrt);

  const totalSqrt = sqrtValues.reduce((sum, val) => sum + val, 0);

  const adjustedPercentages = sqrtValues.map((val) => (val / totalSqrt) * 100);

  return adjustedPercentages.map((val) => Number(val.toFixed(1)));
};

export const polarPadding = (value: number[]): number => {
  const total = value.reduce((acc, val) => acc + val, 0);
  const arr = value.map((item) => item / total);
  const highestNumber = Math.max(...arr);
  switch (true) {
    case highestNumber <= 0.6:
      return value.length === 2 ? 0 : 20;
    case highestNumber > 0.6 && highestNumber <= 0.8:
      return 60;
    case highestNumber > 0.8:
      return 90;
    default:
      return 0;
  }
};
