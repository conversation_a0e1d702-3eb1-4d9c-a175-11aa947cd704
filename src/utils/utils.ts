import {
  AudienceCategories,
  AVATAR_TYPE,
  CityValue,
  TAttribute,
  TDataNumber,
  TSelectOption,
  TYPE_SOCIAL
} from '@/utils/constant';
import isArray from 'lodash/isArray';

export const fNumberToString = (number: string | number = 0) => {
  return number.toLocaleString();
};
export const removeFormatNumber = (numberString: string): number => {
  return Number(numberString.replace(/,/g, ""));
};

export function getLastCharName(name: string, position: "first" | "last") {
  if (!name) return "";
  const nameArr = name.split(" ");
  return position === "first" ? nameArr[0][0] : nameArr[nameArr.length - 1][0];
}
export function formatPayloadAvatar(
  type: TYPE_SOCIAL,
  fb_uid: string,
  actor_id: string
): {
  uid: string;
  type: AVATAR_TYPE;
} {
  const mapType: Record<TYPE_SOCIAL, { uid: string; type: AVATAR_TYPE }> = {
    [TYPE_SOCIAL.group]: {
      uid: fb_uid,
      type: "group",
    },
    [TYPE_SOCIAL.fanpage]: {
      uid: fb_uid,
      type: "page",
    },
    [TYPE_SOCIAL.post]: {
      uid: actor_id,
      type: "page",
    },
    [TYPE_SOCIAL.profile]: {
      uid: fb_uid,
      type: "profile",
    },
  };

  return mapType[type];
}
export const rangePagination = (start: number, end: number) => {
  const length = end - start + 1;

  return Array.from({ length }, (_, idx) => idx + start);
};

export function removeVietnameseTones(str: string) {
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
  str = str.replace(/đ/g, "d");
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
  str = str.replace(/Đ/g, "D");
  // Some system encode vietnamese combining accent as individual utf-8 characters
  // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
  // Remove extra spaces
  // Bỏ các khoảng trắng liền nhau
  str = str.replace(/ + /g, " ");
  str = str.trim();
  // Remove punctuations
  // Bỏ dấu câu, kí tự đặc biệt
  str = str.replace(
    /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,
    " "
  );
  return str;
}
//option default
export const formatOptionSelect = (option: TAttribute = {}, checked: boolean): TSelectOption => {
  const { name = "", id = "" } = option;
  return { label: name, value: id, checked };
};
//filter checked
export const filterIsShowOptions = (attributes: TAttribute[], checkedArr: any = []) => {
  return attributes?.length
    ? attributes.reduce((prevArr: TSelectOption[], current: TAttribute) => {
      const checked = isArray(checkedArr) && !!checkedArr?.includes(current.id);
      return [...prevArr, formatOptionSelect(current, checked)];
    }, [])
    : [];
};
export const toDataOptions = (data: any, idKey = "id", nameKey = "name") => {
  return (data || []).map((dataItem: any) => {
    return {
      id: dataItem[idKey],
      name: dataItem[nameKey],
    };
  });
};

export const formatDate = (dateString: string | Date, isTime?: boolean) => {
  if (!dateString) return "-";
  const date = new Date(dateString);

  // Lấy năm, tháng, ngày
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  // Lấy giờ và phút
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return isTime ? `${day}/${month}/${year} ${hours}:${minutes}` : `${day}/${month}/${year}`;
};
export const formatLabel = (label: string | null | undefined): string => {
  return label
    ? label
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ")
    : "--";
};

export const getCityData = (data: TDataNumber, type?: string): CityValue[] => {
  const cityValueArray: CityValue[] = Object.keys(data).map((key) => ({
    city: key === "NULL" ? "Unknown" : key,
    count: data[key],
  }));
  cityValueArray.sort((a, b) => b.count - a.count);

  return type == "social"
    ? cityValueArray.filter((item) => item.city !== "Unknown").slice(0, 10)
    : cityValueArray.slice(0, 10);
};
export function getAgeValue(age: string) {
  if (age === null) {
    // sort follow ASCII
    // Extended ASCII
    return 128;
  }
  if (age.match(/^<\d+$/)) {
    // Trường hợp có ký tự '<'
    return parseInt(age.substring(1)) - 1;
  } else if (age.match(/^>\d+$/)) {
    // Trường hợp có ký tự '>'
    return parseInt(age.substring(1)) + 1;
  } else if (age.match(/^\d+-\d+$/)) {
    // Trường hợp có ký tự '-'
    const parts = age.split("-");
    return parseInt(parts[1]);
  } else {
    // Trường hợp chuỗi không chứa các ký tự đặc biệt
    return parseInt(age, 10);
  }
}
export function capitalizeOfEachWord(str: string) {
  if (!str) return "";
  return str
  .split(" ")
  .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
  .join(" ");
}

export const getCategoryName = (data: {
  categoryCode?: string, categories: AudienceCategories
}) => {
  const { categoryCode, categories } = data;
  return categories?.items?.find((cate) => cate.code === categoryCode)?.name;
};
