import { packageOptions, TSocialFilterProps, TypeSelectMS } from '@/utils/constant';
import { filterIsShowOptions, toDataOptions } from '@/utils/utils';
import { handleSelectedValue } from '@/utils/filter';

const filterOptions = ({
  isFilterType,
  isFilterCategory,
  isFilterPackage,
  params,
  categories
}: TSocialFilterProps & {params?: any}) => {
  return [
    isFilterType
      ? {
        key: 'type__in',
        placeholder: 'Type',
        type: 'select',
        options: filterIsShowOptions(TypeSelectMS),
        selected: handleSelectedValue(params, 'type__in')
      }
      : null,
    isFilterCategory
      ? {
        key: 'category__in',
        placeholder: 'Category',
        type: 'select',
        options: filterIsShowOptions(toDataOptions(categories?.items ?? [], 'code', 'name')),
        selected: handleSelectedValue(params, 'category__in')
      }
      : null,
    isFilterPackage
      ? {
        key: 'package__in',
        placeholder: 'Package',
        type: 'select',
        options: filterIsShowOptions(packageOptions),
        selected: handleSelectedValue(params, 'package__in')
      }
      : null
  ];
};

export default filterOptions;
