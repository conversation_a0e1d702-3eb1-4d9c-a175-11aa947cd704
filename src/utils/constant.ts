export const HOST_APP = process.env.LANDING_HOST ?? 'https://big360.ai';
export const SUPPORT_MESSENGER = process.env.MESSENGER_URL ?? 'https://m.me/big360ai';
export const APP_DOMAIN = process.env.NEXT_PUBLIC_APP_DOMAIN ?? 'https://app.big360.ai';

export type SortType = 'asc' | 'desc';
export type AVATAR_TYPE = 'profile' | 'group' | 'page' | 'post';

export interface TAttribute {
  id?: string;
  name?: string;
  is_shown?: boolean;
  inputValue?: string;
  type?: string | number;
  code?: string;
  tag?: string;
  disable?: boolean;
  value?: string;
}
export type TSocialDataResponse ={
  count:number;
  items: ITrending[]
}
export interface ITrending {
  fb_uid: string;
  actor_id: string;
  name: string;
  description: string;
  type: number,
  subtype: string;
  system: boolean,
  datatype: string;
  category: string;
  size: number,
  rating: number,
  package: string;
  date_created: string;
  date_updated: string;
  data_release: string;
  is_aud_added: boolean,
  is_ds_added: boolean,
  keywords: string;
}
export type TCategoriesResponse ={
  count:number;
  items: ICategoryItem[]
}
export interface ICategoryItem {
  code: string;
  img_cover: string;
  audience_count: number;
  name: string;
  description?: string;
}

export enum ENDPOINT_TYPE {
  DETAIL = 'detail',
}

export enum TYPE_SOCIAL {
  'group' = 1,
  'fanpage' = 2,
  'profile' = 3,
  'post' = 4,
}

export const SUB_TYPE_SOCIAL_LABEL = [
  { id: 1, name: 'Private Group' },
  { id: 2, name: 'Public Group' },
  { id: 3, name: 'Ads Post' },
  { id: 4, name: 'Live Post' }
];

export const TypeOptions: TAttribute[] = [
  { id: "0", name: "Social Persona" },
  { id: '1', name: 'Group' },
  { id: '2', name: 'Fanpage' },
  { id: '3', name: 'Profile' },
  { id: '4', name: 'Post' }
];

export const TypeSelectMS: TAttribute[] = [
  { id: "1", name: "Group" },
  { id: "2", name: "Fanpage" },
  { id: "3", name: "Profile" },
  { id: "4", name: "Post" },
];

export const packageOptions: TAttribute[] = [
  { id: "bronze", name: "Bronze" },
  { id: "silver", name: "Silver" },
  { id: "gold", name: "Gold" },
  { id: "platinum", name: "Platinum" },
  { id: "diamond", name: "Diamond" },
  { id: "titanium", name: "Titanium" },
];

export interface SelectProps {
  placeholder: string;
  options: TSelectOption[];
  selected?: string;
  defaultValue?: string;
  className?: string;
  onChange: (value: string) => void;
}
export interface TSelectOption {
  label: string;
  value: string | number;
  disabled?: boolean;
  children?: TSelectOption[];
  [keys: string]: any;
}

export interface TSocialFilterProps {
  isFilterPackage?: boolean;
  isFilterType?: boolean;
  isFilterCategory?: boolean;
  PageName?: string;
  className?: string;
  categories?: AudienceCategories
  searchParams?: {[key: string]: string | string[] | undefined}
}
export interface AudienceCategory {
  code: string;
  name: string;
  description: string;
  img_cover: string;
  audience_count: number;
  date_created: string;
  date_updated: string;
}

export interface AudienceCategories {
  count: number;
  items: AudienceCategory[];
}

export interface DimCities {
  items: string[],
  count: number;
}
export interface CityValue {
  city: string;
  count: number;
}
export interface TDataNumber {
  [key: string]: number;
}
export const GENDER = {
  female: "Female",
  male: "Male",
  null: "Unknown",
};
export const DEFAULT_LABELS_GENDER: string[] = [
  "<18",
  "18-24",
  "25-34",
  "35-44",
  "45-54",
  ">54",
  "Unknown",
];
export const TYPE_SOCIAL_LABEL = [
  { id: 1, name: "Group" },
  { id: 2, name: "Fanpage" },
  { id: 3, name: "Profile" },
  { id: 4, name: "Post" },
];
export const audienceType = (type: number) => {
  return TYPE_SOCIAL_LABEL.find((typeSocial) => typeSocial.id === type);
};
