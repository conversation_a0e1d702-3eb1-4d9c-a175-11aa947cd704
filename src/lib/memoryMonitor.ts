// Memory monitoring utility để track và optimize memory usage
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private memoryStats: Array<{ timestamp: number; usage: any }> = [];
  private maxStatsLength = 100; // Giới hạn history

  private constructor() {}

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  // Get current memory usage
  getCurrentMemoryUsage() {
    if (typeof window !== 'undefined') {
      // Browser environment
      return {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        usedJSHeapSize: performance.memory?.usedJSHeapSize || 0,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        totalJSHeapSize: performance.memory?.totalJSHeapSize || 0,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        jsHeapSizeLimit: performance.memory?.jsHeapSizeLimit || 0,
        timestamp: Date.now()
      };
    } else {
      // Node.js environment
      const usage = process.memoryUsage();
      return {
        rss: usage.rss,
        heapTotal: usage.heapTotal,
        heapUsed: usage.heapUsed,
        external: usage.external,
        arrayBuffers: usage.arrayBuffers,
        timestamp: Date.now()
      };
    }
  }

  // Record memory usage
  recordMemoryUsage() {
    const usage = this.getCurrentMemoryUsage() as any;
    this.memoryStats.push(usage);

    // Keep only recent stats
    if (this.memoryStats.length > this.maxStatsLength) {
      this.memoryStats = this.memoryStats.slice(-this.maxStatsLength);
    }

    return usage;
  }

  // Get memory statistics
  getMemoryStats() {
    return {
      current: this.getCurrentMemoryUsage(),
      history: this.memoryStats,
      average: this.calculateAverage(),
      peak: this.getPeakUsage()
    };
  }

  // Calculate average memory usage
  private calculateAverage() {
    if (this.memoryStats.length === 0) return null;

    if (typeof window !== 'undefined') {
      const avgUsed = this.memoryStats.reduce((sum, stat: any) => sum + (stat.usedJSHeapSize || 0), 0) / this.memoryStats.length;
      const avgTotal = this.memoryStats.reduce((sum, stat: any) => sum + (stat.totalJSHeapSize || 0), 0) / this.memoryStats.length;
      return { usedJSHeapSize: avgUsed, totalJSHeapSize: avgTotal };
    } else {
      const avgHeapUsed = this.memoryStats.reduce((sum, stat: any) => sum + (stat.heapUsed || 0), 0) / this.memoryStats.length;
      const avgRss = this.memoryStats.reduce((sum, stat: any) => sum + (stat.rss || 0), 0) / this.memoryStats.length;
      return { heapUsed: avgHeapUsed, rss: avgRss };
    }
  }

  // Get peak memory usage
  private getPeakUsage() {
    if (this.memoryStats.length === 0) return null;

    if (typeof window !== 'undefined') {
      const peak = this.memoryStats.reduce((max: any, stat: any) => {
        return (stat.usedJSHeapSize || 0) > (max.usedJSHeapSize || 0) ? stat : max;
      });
      return peak;
    } else {
      const peak = this.memoryStats.reduce((max: any, stat: any) => {
        return (stat.heapUsed || 0) > (max.heapUsed || 0) ? stat : max;
      });
      return peak;
    }
  }

  // Format memory size for display
  formatMemorySize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  // Check if memory usage is high
  isMemoryUsageHigh(): boolean {
    const current = this.getCurrentMemoryUsage();
    
    if (typeof window !== 'undefined') {
      // Browser: check if using more than 80% of available heap
      const usageRatio = (current.usedJSHeapSize || 0) / (current.jsHeapSizeLimit || 1);
      return usageRatio > 0.8;
    } else {
      // Node.js: check if heap usage is more than 1GB
      return (current.heapUsed || 0) > 1024 * 1024 * 1024;
    }
  }

  // Clear memory stats
  clearStats() {
    this.memoryStats = [];
  }

  // Start periodic monitoring
  startMonitoring(intervalMs: number = 30000) { // 30 seconds default
    if (typeof window !== 'undefined') {
      return setInterval(() => {
        this.recordMemoryUsage();
        
        if (this.isMemoryUsageHigh()) {
          console.warn('High memory usage detected:', this.getCurrentMemoryUsage());
        }
      }, intervalMs);
    }
    return null;
  }

  // Stop monitoring
  stopMonitoring(intervalId: NodeJS.Timeout | number | null) {
    if (intervalId) {
      clearInterval(intervalId);
    }
  }
}

// Export singleton instance
export const memoryMonitor = MemoryMonitor.getInstance();

// Utility functions
export const logMemoryUsage = (label?: string) => {
  const usage = memoryMonitor.getCurrentMemoryUsage();
  const formatted = typeof window !== 'undefined' 
    ? `Used: ${memoryMonitor.formatMemorySize(usage.usedJSHeapSize || 0)}, Total: ${memoryMonitor.formatMemorySize(usage.totalJSHeapSize || 0)}`
    : `Heap: ${memoryMonitor.formatMemorySize(usage.heapUsed || 0)}, RSS: ${memoryMonitor.formatMemorySize(usage.rss || 0)}`;
  
  console.log(`${label ? `[${label}] ` : ''}Memory Usage: ${formatted}`);
  return usage;
};

export const checkMemoryLeak = () => {
  const stats = memoryMonitor.getMemoryStats();
  const isHigh = memoryMonitor.isMemoryUsageHigh();
  
  if (isHigh) {
    console.warn('Potential memory leak detected!', stats);
  }
  
  return { isHigh, stats };
};
