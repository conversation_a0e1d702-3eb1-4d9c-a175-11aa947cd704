import { NextRequest, NextResponse } from 'next/server';
import logger from './logger';

type Handler = (req: NextRequest) => Promise<NextResponse>;

// Cache để tránh tạo function mới mỗi lần
const loggerCache = new WeakMap<Handler, Handler>();

export function withLogger(handler: Handler): Handler {
  // Kiểm tra cache trước
  if (loggerCache.has(handler)) {
    return loggerCache.get(handler)!;
  }

  const wrappedHandler = async function (req: NextRequest) {
    // Chỉ log trong development hoặc khi có LOG_LEVEL
    if (process.env.NODE_ENV === 'test') {
      return handler(req);
    }

    const method = req.method;
    const url = req.nextUrl.pathname;
    const start = Date.now();

    // Tối ưu IP extraction
    const ip = req.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
               req.headers.get('x-real-ip') ||
               'Unknown';

    let body = '';

    // Chỉ log body cho non-GET requests và khi cần thiết
    if (method !== 'GET' && process.env.LOG_LEVEL === 'debug') {
      try {
        const clone = req.clone();
        const text = await clone.text();
        // Giới hạn body size để tránh memory issues
        body = text.length > 1000 ? `${text.substring(0, 1000)}...` : text;
      } catch {
        body = '[unreadable]';
      }
    }

    // Simplified logging
    if (process.env.NODE_ENV === 'development' || process.env.LOG_LEVEL === 'debug') {
      logger.info(`${method} ${url} from ${ip}${body ? ` | Body: ${body}` : ''}`);
    }

    let response: NextResponse;
    try {
      response = await handler(req);
    } catch (error) {
      logger.error(`Error in ${method} ${url}: ${error}`);
      throw error;
    }

    const duration = Date.now() - start;

    // Chỉ log response khi cần thiết
    if (process.env.NODE_ENV === 'development' || response.status >= 400) {
      logger.info(`${method} ${url} -> ${response.status} (${duration}ms)`);
    }

    return response;
  };

  // Cache wrapped handler
  loggerCache.set(handler, wrappedHandler);
  return wrappedHandler;
}
