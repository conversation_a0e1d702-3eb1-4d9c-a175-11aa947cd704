import { NextRequest, NextResponse } from 'next/server';
import logger from './logger';

type Handler = (req: NextRequest) => Promise<NextResponse>;

export function withLogger(handler: Handler): Handler {
  return async function (req: NextRequest) {
    const method = req.method;
    const url = req.nextUrl.pathname;
    const start = Date.now();

    // 👇 Lấy IP: dùng header hoặc fallback
    const ip =
      req.headers.get('x-forwarded-for') ||
      'Unknown IP';

    let body = '';

    try {
      // 👇 Chỉ đọc nếu có body (POST/PUT), tránh lỗi ở GET
      if (method !== 'GET') {
        const clone = req.clone(); // cần clone vì req chỉ đọc 1 lần
        const json = await clone.json();
        body = JSON.stringify(json);
      }
    } catch {
      body = '[unreadable body]';
    }
    // request from user
    logger.info(`Request 📥 [${method}] ${url} from ${ip} | Body: ${body || '[empty]'}`);

    const response = await handler(req);

    const duration = Date.now() - start;
    // response to user
    logger.info(`Response 📤 [${method}] ${url} -> ${response.status} | Took ${duration}ms`);

    return response;
  };
}
