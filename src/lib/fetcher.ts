// Memory-optimized fetcher với caching và error handling
const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const MAX_CACHE_SIZE = 100; // Giới hạn cache size

// Cleanup cache khi quá lớn
function cleanupCache() {
  if (cache.size > MAX_CACHE_SIZE) {
    const now = Date.now();
    const entries = Array.from(cache.entries());

    // Xóa expired entries trước
    entries.forEach(([key, value]) => {
      if (now - value.timestamp > value.ttl) {
        cache.delete(key);
      }
    });

    // Nếu vẫn quá lớn, xóa oldest entries
    if (cache.size > MAX_CACHE_SIZE) {
      const sortedEntries = entries
        .sort((a, b) => a[1].timestamp - b[1].timestamp)
        .slice(0, cache.size - MAX_CACHE_SIZE);

      sortedEntries.forEach(([key]) => cache.delete(key));
    }
  }
}

export const fetcher = async (url: string, options?: RequestInit): Promise<any> => {
  // Kiểm tra cache trước
  const cacheKey = `${url}${JSON.stringify(options || {})}`;
  const cached = cache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < cached.ttl) {
    return cached.data;
  }

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // Cache successful responses
    cleanupCache();
    cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      ttl: CACHE_TTL
    });

    return data;
  } catch (error) {
    // Log error nhưng không crash
    if (process.env.NODE_ENV === 'development') {
      console.error('Fetcher error:', error);
    }
    throw error;
  }
};

// Utility để clear cache khi cần
export const clearFetcherCache = () => cache.clear();

// Utility để get cache stats
export const getFetcherCacheStats = () => ({
  size: cache.size,
  keys: Array.from(cache.keys())
});
