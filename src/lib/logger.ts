import { createLogger, format, transports } from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs';

// Lazy initialization để tránh tạo logger khi không cần thiết
let loggerInstance: any = null;

function cleanOldLogs(dir: string, keep = 3) { // Giảm từ 5 xuống 3 files
  try {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir)
      .filter(f => /^app-\d{4}-\d{2}-\d{2}\.log$/.test(f))
      .sort((a, b) => fs.statSync(path.join(dir, b)).mtimeMs - fs.statSync(path.join(dir, a)).mtimeMs);

    const old = files.slice(keep);
    old.forEach(f => {
      try {
        fs.unlinkSync(path.join(dir, f));
      } catch (error) {
        // Silent fail để tránh crash
      }
    });
  } catch (error) {
    // Silent fail
  }
}

function createLoggerInstance() {
  const logDir = path.join(process.cwd(), 'logs');
  const logLevel = process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'warn' : 'info');

  // Chỉ tạo log directory khi thực sự cần
  if (!fs.existsSync(logDir)) {
    try {
      fs.mkdirSync(logDir, { recursive: true });
    } catch (error) {
      // Fallback to console only if can't create log dir
      return createLogger({
        level: logLevel,
        format: format.simple(),
        transports: [new transports.Console()]
      });
    }
  } else {
    cleanOldLogs(logDir, 3);
  }

  // Optimized format để giảm memory
  const simpleFormat = format.printf(({ timestamp, level, message }) =>
    `${timestamp} ${level}: ${message}`
  );

  const transportsArray = [
    // Console transport với format tối giản
    new transports.Console({
      level: logLevel,
      format: process.env.NODE_ENV === 'production'
        ? simpleFormat
        : format.combine(format.colorize(), simpleFormat),
      silent: process.env.NODE_ENV === 'test' // Tắt console log khi test
    })
  ];

  // Chỉ thêm file transport trong production
  if (process.env.NODE_ENV === 'production') {
    transportsArray.push(
      new DailyRotateFile({
        dirname: logDir,
        filename: 'app-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        maxSize: '2m', // Giảm từ 5m xuống 2m
        maxFiles: '3d', // Chỉ giữ 3 ngày
        zippedArchive: true, // Nén để tiết kiệm dung lượng
        auditFile: path.join(logDir, '.audit.json'),
        options: { flags: 'a', highWaterMark: 16 } // Giảm buffer size
      })
    );
  }

  return createLogger({
    level: logLevel,
    format: format.combine(
      format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      simpleFormat
    ),
    transports: transportsArray,
    exitOnError: false, // Không exit khi có lỗi
    silent: process.env.NODE_ENV === 'test'
  });
}

// Lazy getter
function getLogger() {
  if (!loggerInstance) {
    loggerInstance = createLoggerInstance();
    if (process.env.NODE_ENV !== 'test') {
      loggerInstance.info('🟢 Logger initialized');
    }
  }
  return loggerInstance;
}

// Export proxy object để lazy load
export default new Proxy({} as any, {
  get(target, prop) {
    return getLogger()[prop];
  }
});
