import httpInstance from '@/api/index';
import { SocialPersonaDimType } from '@/utils/SocialData';

const get = async <T = any>({ params, endpoint = "" }: { params?: any; endpoint?: string }) => {
  try {
    const response = await httpInstance.get<T>(`/dataset-library/social-audience/${endpoint}`, { params });
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

const getAvatar = async (fb_uid: string, type: string) => {
  try {
    const res =  await fetch(`/api/fb-img/${fb_uid}?type=${type}`);
    return await res.json()
  } catch (error) {
    return { data: null, error };
  }
};

const getDims = async (type: SocialPersonaDimType)=>{
  try {
    const dimsResponse = await httpInstance.get(`/social-persona/dims/${ type }/`);
    return dimsResponse.data.data;
  }catch (error) {
    return { data: null, error };
  }
}


export const socialAPI = {
  get,
  getAvatar,
  getDims
};
