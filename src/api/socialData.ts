import httpInstance from '@/api/index';
import { SocialPersonaDimType } from '@/utils/SocialData';

// Avatar cache để tránh fetch lại
const avatarCache = new Map<string, { data: any; timestamp: number }>();
const AVATAR_CACHE_TTL = 30 * 60 * 1000; // 30 minutes

const get = async <T = any>({ params, endpoint = "" }: { params?: any; endpoint?: string }) => {
  try {
    const response = await httpInstance.get<T>(`/dataset-library/social-audience/${endpoint}`, {
      params,
      // Add request timeout
      timeout: 15000
    });
    return response.data;
  } catch (error: any) {
    // Better error handling
    const errorResponse = {
      data: null,
      error: {
        message: error?.message || 'Unknown error',
        status: error?.response?.status,
        endpoint: `/dataset-library/social-audience/${endpoint}`
      }
    };

    if (process.env.NODE_ENV === 'development') {
      console.error('SocialAPI get error:', errorResponse);
    }

    return errorResponse;
  }
};

const getAvatar = async (fb_uid: string, type: string): Promise<any> => {
  const cacheKey = `${fb_uid}-${type}`;

  // Check cache first
  const cached = avatarCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < AVATAR_CACHE_TTL) {
    return cached.data;
  }

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8s timeout

    const res = await fetch(`/api/fb-img/${fb_uid}?type=${type}`, {
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    clearTimeout(timeoutId);

    if (!res.ok) {
      throw new Error(`HTTP ${res.status}: ${res.statusText}`);
    }

    const data = await res.json();

    // Cache successful response
    avatarCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });

    // Cleanup old cache entries
    if (avatarCache.size > 100) {
      const entries = Array.from(avatarCache.entries());
      const oldEntries = entries
        .filter(([_, value]) => Date.now() - value.timestamp > AVATAR_CACHE_TTL)
        .slice(0, 20);

      oldEntries.forEach(([key]) => avatarCache.delete(key));
    }

    return data;
  } catch (error: any) {
    const errorResponse = {
      data: null,
      error: {
        message: error?.message || 'Avatar fetch failed',
        fb_uid,
        type
      }
    };

    if (process.env.NODE_ENV === 'development') {
      console.error('Avatar fetch error:', errorResponse);
    }

    return errorResponse;
  }
};

const getDims = async (type: SocialPersonaDimType) => {
  try {
    const dimsResponse = await httpInstance.get(`/social-persona/dims/${type}/`, {
      timeout: 10000
    });
    return dimsResponse.data?.data || dimsResponse.data;
  } catch (error: any) {
    const errorResponse = {
      data: null,
      error: {
        message: error?.message || 'Dims fetch failed',
        type,
        status: error?.response?.status
      }
    };

    if (process.env.NODE_ENV === 'development') {
      console.error('Dims fetch error:', errorResponse);
    }

    return errorResponse;
  }
};

// Utility functions
const clearAvatarCache = () => avatarCache.clear();
const getAvatarCacheStats = () => ({
  size: avatarCache.size,
  keys: Array.from(avatarCache.keys())
});

export const socialAPI = {
  get,
  getAvatar,
  getDims,
  clearAvatarCache,
  getAvatarCacheStats
};
