import axios, { AxiosRequestConfig } from 'axios';

const accessToken = process.env.NEXT_PUBLIC_TOKEN ?? '';

const AXIOS_OPTIONS = {
  baseURL: process.env.NEXT_PUBLIC_API_URL + '/api/v1',
  headers: {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': '69420',
    'x-api-key': accessToken
  }
};


function createHttp(options: AxiosRequestConfig) {
  const instance = axios.create(options);

  const setupInterceptors = () => {
    instance.interceptors.response.use(handleResponse, handleError);
  };

  const handleResponse = (response: any) => {
    return response;
  };

  const handleError = (error: any) => {
    return error?.response?.data || error?.response || error || {};
  };

  setupInterceptors();
  return {
    getInstance: () => instance
  };
}

const httpInstance = createHttp(AXIOS_OPTIONS).getInstance();
export default httpInstance;
