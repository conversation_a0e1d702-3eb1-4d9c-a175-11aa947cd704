import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

const accessToken = process.env.NEXT_PUBLIC_TOKEN ?? '';

// Optimized axios configuration
const AXIOS_OPTIONS: AxiosRequestConfig = {
  baseURL: process.env.NEXT_PUBLIC_API_URL + '/api/v1',
  timeout: 10000, // 10s timeout
  headers: {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': '69420',
    'x-api-key': accessToken
  },
  // Connection pooling và performance optimizations
  maxRedirects: 3,
  maxContentLength: 50 * 1024 * 1024, // 50MB max
  maxBodyLength: 50 * 1024 * 1024,
  // Reuse connections
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  httpAgent: typeof window === 'undefined' ? new (require('http').Agent)({
    keepAlive: true,
    maxSockets: 10,
    maxFreeSockets: 5,
    timeout: 60000,
    freeSocketTimeout: 30000
  }) : undefined,
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  httpsAgent: typeof window === 'undefined' ? new (require('https').Agent)({
    keepAlive: true,
    maxSockets: 10,
    maxFreeSockets: 5,
    timeout: 60000,
    freeSocketTimeout: 30000
  }) : undefined
};

// Request/Response cache
const responseCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes

function createHttp(options: AxiosRequestConfig) {
  const instance = axios.create(options);

  // Request interceptor để add caching
  instance.interceptors.request.use(
    (config) => {
      // Chỉ cache GET requests
      if (config.method === 'get') {
        const cacheKey = `${config.url}${JSON.stringify(config.params || {})}`;
        const cached = responseCache.get(cacheKey);

        if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
          // Return cached response
          return Promise.reject({
            __cached: true,
            data: cached.data
          });
        }
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // Cache successful GET responses
      if (response.config.method === 'get' && response.status === 200) {
        const cacheKey = `${response.config.url}${JSON.stringify(response.config.params || {})}`;
        responseCache.set(cacheKey, {
          data: response,
          timestamp: Date.now()
        });

        // Cleanup old cache entries
        if (responseCache.size > 50) {
          const entries = Array.from(responseCache.entries());
          const oldEntries = entries
            .filter(([_, value]) => Date.now() - value.timestamp > CACHE_DURATION)
            .slice(0, 10);

          oldEntries.forEach(([key]) => responseCache.delete(key));
        }
      }

      return response;
    },
    (error: AxiosError | any) => {
      // Handle cached responses
      if (error.__cached) {
        return Promise.resolve(error.data);
      }

      // Improved error handling
      const errorResponse = {
        data: null,
        error: {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        }
      };

      // Log errors in development
      if (process.env.NODE_ENV === 'development') {
        console.error('API Error:', errorResponse, error);
      }

      return Promise.resolve(errorResponse);
    }
  );

  return {
    getInstance: () => instance,
    clearCache: () => responseCache.clear(),
    getCacheStats: () => ({
      size: responseCache.size,
      keys: Array.from(responseCache.keys())
    })
  };
}

const httpClient = createHttp(AXIOS_OPTIONS);
const httpInstance = httpClient.getInstance();

// Export utilities
export const clearApiCache = httpClient.clearCache;
export const getApiCacheStats = httpClient.getCacheStats;
export default httpInstance;
