'use client';
import { useEffect, useRef, useCallback, useMemo, useState } from 'react';
import { memoryMonitor } from '@/lib/memoryMonitor';

// Hook để optimize performance và memory usage
export const usePerformanceOptimization = (componentName?: string) => {
  const renderCountRef = useRef(0);
  const mountTimeRef = useRef<number>(0);
  const memoryIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Track component renders
  renderCountRef.current += 1;

  // Mount time tracking
  useEffect(() => {
    mountTimeRef.current = Date.now();
    
    if (process.env.NODE_ENV === 'development' && componentName) {
      console.log(`🚀 [${componentName}] Component mounted`);
      memoryMonitor.recordMemoryUsage();
    }

    return () => {
      if (process.env.NODE_ENV === 'development' && componentName) {
        const mountDuration = Date.now() - mountTimeRef.current;
        console.log(`🔄 [${componentName}] Component unmounted after ${mountDuration}ms, renders: ${renderCountRef.current}`);
      }
    };
  }, [componentName]);

  // Memory monitoring
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      memoryIntervalRef.current = memoryMonitor.startMonitoring(60000); // Check every minute
    }

    return () => {
      if (memoryIntervalRef.current) {
        memoryMonitor.stopMonitoring(memoryIntervalRef.current);
      }
    };
  }, []);

  // Debounced function creator
  const createDebouncedCallback = useCallback((
    callback: (...args: any[]) => void,
    delay: number = 300
  ) => {
    const timeoutRef = useRef<NodeJS.Timeout>();

    return useCallback((...args: any[]) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    }, [callback, delay]);
  }, []);

  // Throttled function creator
  const createThrottledCallback = useCallback((
    callback: (...args: any[]) => void,
    delay: number = 300
  ) => {
    const lastCallRef = useRef<number>(0);

    return useCallback((...args: any[]) => {
      const now = Date.now();
      if (now - lastCallRef.current >= delay) {
        lastCallRef.current = now;
        callback(...args);
      }
    }, [callback, delay]);
  }, []);

  // Memoized value creator với custom comparison
  const createMemoizedValue = useCallback(<T>(
    factory: () => T,
    deps: React.DependencyList,
    compare?: (prev: T, next: T) => boolean
  ) => {
    return useMemo(() => {
      const value = factory();
      
      if (process.env.NODE_ENV === 'development' && componentName) {
        console.log(`📝 [${componentName}] Memoized value recalculated`);
      }
      
      return value;
    }, deps);
  }, [componentName]);

  // Performance measurement
  const measurePerformance = useCallback((label: string, fn: () => void) => {
    if (process.env.NODE_ENV === 'development') {
      const start = performance.now();
      fn();
      const end = performance.now();
      console.log(`⏱️ [${componentName || 'Unknown'}] ${label}: ${(end - start).toFixed(2)}ms`);
    } else {
      fn();
    }
  }, [componentName]);

  // Memory usage checker
  const checkMemoryUsage = useCallback(() => {
    if (process.env.NODE_ENV === 'development') {
      const usage = memoryMonitor.getCurrentMemoryUsage();
      const isHigh = memoryMonitor.isMemoryUsageHigh();
      
      if (isHigh) {
        console.warn(`🚨 [${componentName || 'Unknown'}] High memory usage detected:`, usage);
      }
      
      return { usage, isHigh };
    }
    return null;
  }, [componentName]);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (memoryIntervalRef.current) {
      memoryMonitor.stopMonitoring(memoryIntervalRef.current);
      memoryIntervalRef.current = null;
    }
  }, []);

  return {
    renderCount: renderCountRef.current,
    createDebouncedCallback,
    createThrottledCallback,
    createMemoizedValue,
    measurePerformance,
    checkMemoryUsage,
    cleanup
  };
};

// Hook để optimize large lists
export const useVirtualization = (
  items: any[],
  itemHeight: number,
  containerHeight: number
) => {
  const [startIndex, setStartIndex] = useState(0);
  const [endIndex, setEndIndex] = useState(0);

  const visibleItems = useMemo(() => {
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const buffer = Math.floor(visibleCount * 0.5); // 50% buffer
    
    const start = Math.max(0, startIndex - buffer);
    const end = Math.min(items.length, startIndex + visibleCount + buffer);
    
    return items.slice(start, end).map((item, index) => ({
      ...item,
      originalIndex: start + index
    }));
  }, [items, startIndex, itemHeight, containerHeight]);

  const handleScroll = useCallback((scrollTop: number) => {
    const newStartIndex = Math.floor(scrollTop / itemHeight);
    setStartIndex(newStartIndex);
    
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    setEndIndex(newStartIndex + visibleCount);
  }, [itemHeight, containerHeight]);

  return {
    visibleItems,
    handleScroll,
    totalHeight: items.length * itemHeight,
    startIndex,
    endIndex
  };
};

// Hook để optimize images
export const useImageOptimization = () => {
  const imageCache = useRef(new Map<string, HTMLImageElement>());

  const preloadImage = useCallback((src: string): Promise<HTMLImageElement> => {
    if (imageCache.current.has(src)) {
      return Promise.resolve(imageCache.current.get(src)!);
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        imageCache.current.set(src, img);
        resolve(img);
      };
      img.onerror = reject;
      img.src = src;
    });
  }, []);

  const clearImageCache = useCallback(() => {
    imageCache.current.clear();
  }, []);

  return {
    preloadImage,
    clearImageCache,
    cacheSize: imageCache.current.size
  };
};
