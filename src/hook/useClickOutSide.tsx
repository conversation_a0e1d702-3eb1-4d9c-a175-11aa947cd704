'use client';
import { useEffect } from "react";

const useOutsideClick = (
  ref: React.MutableRefObject<HTMLDivElement | null>,
  refSecond: React.MutableRefObject<HTMLDivElement | null>,
  callback: () => void
) => {
  const handleClick = (e: any) => {
    if (refSecond.current) {
      if (
        ref &&
        ref?.current &&
        !ref?.current?.contains(e.target) &&
        refSecond &&
        refSecond?.current &&
        !refSecond?.current?.contains(e.target)
      ) {
        callback();
      }
    } else {
      if (ref && ref?.current && !ref?.current?.contains(e.target)) {
        callback();
      }
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClick);
    return () => {
      document.removeEventListener("click", handleClick);
    };
  });
};

export default useOutsideClick;
