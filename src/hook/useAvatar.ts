'use client'
import { useEffect, useState } from "react";
import { AVATAR_TYPE } from '@/utils/constant';
import { socialAPI } from '@/api/socialData';

type Props = {
  type: AVATAR_TYPE;
  uid: string;
};
interface IAvatar {
  loading: boolean;
  url: string;
}

const useAvatar = ({ type, uid }: Props) => {
  const [avatar, setAvatar] = useState<IAvatar>({
    loading: false,
    url: "",
  });

  useEffect(() => {
    if (uid) {
      fetchAvatar().finally(()=>{});
    }
  }, [uid]);

  const fetchAvatar = async () => {
    setAvatar({ url: "", loading: true });
    const res = await socialAPI.getAvatar(uid, type);
    if (res) {
      setAvatar({ url: res, loading: false });
    } else {
      setAvatar({ url: "", loading: false });
    }
  };

  return { avatar };
};
export default useAvatar;
