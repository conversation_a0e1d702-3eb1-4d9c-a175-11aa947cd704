'use client';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay } from 'swiper/modules';
import Image from 'next/image';
import React from 'react';

import habLogo from '@/assets/partners/hab-logo.png';
import hoaianBeautyLogo from '@/assets/partners/hoaian-beauty-logo.png';
import bizzi<PERSON>ogo from '@/assets/partners/bizzi-logo.png';
import miaLogo from '@/assets/partners/mia-logo.png';
import jp24Logo from '@/assets/partners/jp24-logo.png';
import tamLuxuryLogo from '@/assets/partners/tam-luxury-logo.png';
import enzyLogo from '@/assets/partners/enzy-logo.png';

const logoPartners = [
  { id: 1, src: habLogo, alt: 'hab-logo' },
  { id: 2, src: hoaianBeautyLogo, alt: 'hoaian-beauty-logo' },
  { id: 3, src: bizzi<PERSON>ogo, alt: 'bizzi-logo' },
  { id: 4, src: mia<PERSON><PERSON>, alt: 'mia-logo' },
  { id: 5, src: jp24<PERSON>ogo, alt: 'jp-24-logo' },
  { id: 6, src: tamLuxuryLogo, alt: 'tam-luxury-logo' },
  { id: 7, src: enzyLogo, alt: 'enzy-logo' }
];
export const Partners = () => {
  return (
    <div className="w-full max-w-[960px] mx-auto">
      <Swiper
        modules={[Autoplay]}
        spaceBetween={16}
        loop={true}
        freeMode={true}
        slidesPerView={2}
        speed={4000}
        className="swiper-remove-time-transition"
        autoplay={{
          delay: 0,
          disableOnInteraction: false,
          pauseOnMouseEnter: false
        }}
        breakpoints={{
          475: {
            slidesPerView: 3
          },
          768: {
            slidesPerView: 4
          },
          992: {
            slidesPerView: 5
          }
        }}
      >
        {logoPartners.map((slide) => (
          <SwiperSlide key={slide.id}>
            <div className="flex items-center justify-center">
              <Image
                src={slide.src}
                alt={slide.alt}
                quality={100}
                width={slide.src.width / 2}
                height={slide.src.height / 2}
              />
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};
