import React from 'react';

type TProps = {
  title?: React.ReactNode;
  titleRule?: React.ReactNode;
  description?: React.ReactNode;
}
export const RuleContent: React.FC<TProps> = (props: TProps) => {
  const { title, titleRule, description } = props;
  return (
    <>
      {title && <h2 className="text-primary text-[24px] font-semibold mb-[24px] mt-[40px]">
        {title}
      </h2>}

      <div className="text-secondary text-base mb-4">
        <span className="font-medium text-primary">{titleRule}{' '}</span>
        <span>{description}</span>
      </div>
    </>
  );
};
