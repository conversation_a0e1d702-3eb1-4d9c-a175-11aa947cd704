'use client';
import React from 'react';
import { Autoplay } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import Image from 'next/image';

const quoteSlide = [
  {
    id: 1,
    quote:
      'Thanks to this big data solution, we’ve personalized our marketing like never before — better customer insights and real-time analytics boosted conversions by 35% in just three months!',
    name: 'Mrs. <PERSON>',
    position: 'CMO - HAB Beauty',
    imgSrc: '/partners/hab-logo.png',
    imgName: 'hab-logo',
    imgSize: {
      width: 48, height: 24
    }
  },
  {
    id: 2,
    quote:
      'This platform transformed our lead generation! With high-quality data and seamless CRM integration, our sales team doubled outreach efficiency with unmatched insights.',
    name: 'Mr. <PERSON>',
    position: 'Sales Director - Bizzi',
    imgSrc: '/partners/bizzi-logo.png',
    imgName: 'bizzi-logo',
    imgSize: {
      width: 69,
      height: 24
    }
  },
  {
    id: 3,
    quote:
      'This platform changed how we make decisions. AI insights and automation save us hours every week — and our customer retention has never been better!',
    name: 'Mr. <PERSON><PERSON>',
    position: 'Marketing Director - JP24',
    imgSrc: '/partners/jp24-logo.png',
    imgName: 'jp24-logo',
    imgSize: {
      width: 79,
      height: 24
    }
  }
];

export const SwiperContainer = () => {
  return (
    <div className="w-full max-w-[960px] mx-auto">
      <Swiper
        loop={true}
        slidesPerView="auto"
        spaceBetween={30}
        speed={2500}
        autoplay={{
          delay: 2500,
          disableOnInteraction: false
        }}
        modules={[Autoplay]}
      >
        {quoteSlide.map((slide) => (
          <SwiperSlide key={slide.id}>
            <div className="w-full md:max-w-[600px] lg:max-w-[920px] my-[40px] lg:mx-[24px] relative mx-auto">
              <Image
                src={'/doubleQuotes.svg'}
                alt="doubleQuotes"
                width={24}
                height={20}
                className="absolute top-0 left-0"
              />
              <p className="pt-[20px] px-[28px] text-[14px] md:text-xl lg:text-2xl font-medium text-primary">
                {slide.quote}
              </p>
              <div className="mt-[16px]">
                <p className="text-right ">
                  {slide.name}
                  <br />
                  {slide.position}
                </p>
                <div className="ml-auto mt-[12px] lg:mt-[10px]" style={{
                  width: slide.imgSize.width+'px',
                  height: slide.imgSize.height+'px',
                }}>
                  <Image
                    src={slide.imgSrc} alt={slide.imgName} quality={100}
                    width={0}
                    height={0}
                    sizes="100vw"
                    className="brightness-0"
                    style={{
                      width: slide.imgSize.width+'px',
                      height: slide.imgSize.height+'px',
                    }}
                  />
                </div>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};
