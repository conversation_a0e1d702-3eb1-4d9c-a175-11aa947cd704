import React from 'react';

interface IHeaderPageProps {
  title?: React.ReactNode;
}

export const HeaderPage = ({ title }: IHeaderPageProps) => {
  return (
    <h1 className="text-2xl mb-3 lg:text-5xl font-semibold lg:mb-[24px]">
      {title}
    </h1>
  );
};

interface IHeaderSecondary {
  title?: React.ReactNode;
}

export const HeaderSecondary = ({ title }: IHeaderSecondary) => {
  return (
    <h2 className="text-lg lg:text-4xl font-semibold mb-6">
      {title}
    </h2>
  );
};
