import { CSSProperties, useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
} from "@tanstack/react-table";
import CustomNoRowsOverlay from '@/components/DataTable/CustomNoRowsOverlay';
import { cn } from '@/lib/utils';
import { LoadingButtonIcon } from '@/assets/icon/LoadingButtonIcon';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue & { customField?: string }>[];
  data: TData[];
  isPagination?: any;
  classTable?: string;
  cellClass?: string;
  loading?: boolean;
  // oncChangePagination: () => void;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  cellClass,
  classTable,
  loading = false,
}: // onChangePagination,
DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const table = useReactTable({
    data,
    columns,
    state: { sorting },
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const getCommonPinningStyles = (index: number): CSSProperties => {
    const columnTable = columns[index];
    const meta = columnTable.meta as { sticky?: string | undefined; position?: number | undefined };
    const sticky = meta?.sticky as 'left' | 'right' | undefined;
    if (sticky !=='left' && sticky !== 'right') {
      return {};
    }
    // const shadow = '1px 0px 0px 0px #A7AAB1 inset';
    return {
      // boxShadow: sticky === 'left' ? '-' + shadow : sticky === 'right' ? shadow : 'none',
      left: sticky === 'left' ? `${meta.position ?? 0}px` : undefined,
      right: sticky === 'right' ? `${meta.position ?? 0}px` : undefined,
      position: sticky ? 'sticky' : 'relative',
      zIndex: sticky ? 1 : 0,
    };
  };

  return (
    <div>
      <div
        className={cn(
          "rounded-2xl overflow-hidden shadow-sm border-[2px] border-secondary min-h-[700px] relative",
          classTable,
          table.getRowModel().rows?.length == 0 && !loading && "min-h-[500px]"
        )}
      >
        <Table className="">
          <TableHeader className="bg-secondary text-secondary font-medium ">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="border-none">
                {headerGroup.headers.map((header,index) => {
                  return !header.column.columnDef.enableHiding && (
                    <TableHead
                      key={header.id}
                      className={cn("text-center py-4 bg-secondary", header.column.columnDef.meta)}
                      style={{
                        ...getCommonPinningStyles(index),
                      }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          {table.getRowModel().rows?.length > 0 && !loading && (
            <TableBody className="items-start">
              {table.getRowModel().rows.map((row) => {
                const isHighLight = !!(row.original as any)?.isHighLight;
                return <TableRow
                  className={cn('h-fit group group', isHighLight ? '!bg-[#eef9ff]' : '')}
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell, index) => {
                    return !cell.column.columnDef.enableHiding &&
                      <TableCell
                        key={cell.id} className={cn('border-primary bg-white group-hover:bg-muted/50',isHighLight ? '!bg-[#eef9ff]' : '', cellClass)}
                        style={{
                          ...getCommonPinningStyles(index)
                        }}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>;
                  })}
                </TableRow>
              })}
            </TableBody>
          )}
        </Table>
        {loading && (
          <div className="flex justify-center items-center absolute w-full h-full text-lg">
            <LoadingButtonIcon />
          </div>
        )}
        {table.getRowModel().rows?.length == 0 && !loading && (
          <div className="flex justify-center items-center absolute w-full h-full text-lg">
            <CustomNoRowsOverlay />
          </div>
        )}
      </div>
    </div>
  );
}
