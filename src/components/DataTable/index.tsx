'use client'
import {
  AllCommunityModule,
  ColDef, ModuleRegistry,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy
} from 'ag-grid-community';
import { AgGridReact } from "ag-grid-react";
import styled from "styled-components";

import { useCallback, useRef } from 'react';
import CustomLoadingOverlay from "./CustomLoadingOverlay";
import CustomHeader from "./CustomHeader";
import { cn } from '@/lib/utils';
import { useSearchParams } from 'next/navigation';
ModuleRegistry.registerModules([AllCommunityModule]);

interface TableData<TData = any> {
  column: ColDef[];
  rowData: TData[];
  rowHeight?: number;
  defaultColDef?: ColDef<TData>;
  components?: {
    [p: string]: any;
  };
  suppressRowClickSelection?: boolean;
  singleClickEdit?: boolean;
  suppressClickEdit?: boolean;
  pagination?: boolean;
  headerHeight?: number;
  rowClass?: string;
  autoSizeStrategy?:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy;
  paginationParams?: {
    currentPage?: number;
    pageSize?: number;
    totalCount?: number;
    onPageChange?: (page: number) => void;
  };
  loading?: boolean;
  onSortChanged?: any;
}

const DataTable = (props: TableData & { className?: string }) => {
  const {
    rowHeight = 65,
    defaultColDef = {
      resizable: false,
      sortable: false,
      autoHeight: true,
      suppressAutoSize: true,
    },
    rowData,
    rowClass = "p-2",
    components = { agColumnHeader: CustomHeader },
    column,
    className,
    headerHeight = 36,
    suppressRowClickSelection = false,
    suppressClickEdit = false,
    singleClickEdit = false,
    onSortChanged = () => {},
    loading,
    ...restProps
  } = props;
  const searchParamsUrl = useSearchParams();
  const orderBy= searchParamsUrl.get('order_by')
  const gridRef = useRef<AgGridReact>(null);

  const handleGridReady = (params: any) => {
    params.api?.showLoadingOverlay();
    gridRef?.current?.api?.sizeColumnsToFit();
  };
  const onGridSizeChanged = () => {
    gridRef?.current?.api?.sizeColumnsToFit();
  };

  const postSortRows = useCallback((params: any) => {
    const rowNodes = params.nodes;
    rowNodes.sort((a: any, b: any) => {
      const sizeA = a.data?.size || 0;
      const sizeB = b.data?.size || 0;
      const ratingA = a.data?.rating || 0;
      const ratingB = b.data?.rating || 0;
      switch (orderBy){
        case 'size':
          return sizeA - sizeB;
        case '-size':
          return sizeB - sizeA;
        case 'rating':
          return ratingA - ratingB;
        case '-rating':
          return ratingB - ratingA;
        default:
          return sizeB - sizeA;
      }
    });
    for (let i = 0; i < rowNodes.length; i++) {
      if (rowNodes[i]) {
        rowNodes[i].rowIndex = i;
      }
    }
  }, [orderBy]);

  const templateNodata = `
    <div role="presentation" class="ag-overlay-loading-center ">
      <i class="far fa-frown text-[18px]" aria-live="polite" aria-atomic="true">
        No data
      </i>
    </div>`;

  return (
    <TableWrapper
      className={cn("mt-5 shadow-chart rounded-tl-[10px] rounded-tr-[10px]", className)}
    >
      <AgGridReact
        {...restProps}
        ref={gridRef}
        loadingOverlayComponent={() => <CustomLoadingOverlay />}
        overlayNoRowsTemplate={templateNodata}
        onGridReady={(params) => handleGridReady(params)}
        onGridSizeChanged={() => onGridSizeChanged()}
        loading={loading}
        columnDefs={column}
        defaultColDef={defaultColDef}
        rowData={rowData}
        rowHeight={rowHeight}
        components={components}
        rowClass={rowClass}
        pagination={false}
        paginateChildRows={false}
        headerHeight={headerHeight}
        singleClickEdit={singleClickEdit}
        suppressClickEdit={suppressClickEdit}
        suppressRowClickSelection={suppressRowClickSelection}
        domLayout="autoHeight"
        onSortChanged={onSortChanged}
        suppressMovableColumns={true}
        postSortRows={postSortRows}
        animateRows={true}
      />
    </TableWrapper>
  );
};

const TableWrapper = styled("div")(() => ({
  backgroundColor: "white",
  position: "relative",
  fontSize: "14px",
  "& .ag-root": {
    minHeight: "700px",
  },
  "& .ag-header": {
    backgroundColor: "#F7F7F8",
    padding: "0 12px",
    borderTopLeftRadius: "10px",
    borderTopRightRadius: "10px",
    fontWeight: "500",
  },
  "& .ag-header-cell": {
    padding: "12px 0",
  },
  "& .ag-center-cols-viewport": {
    padding: "0 12px",
  },
  "& .ag-row": {
    display: "flex",
    padding: "0px",
    "& .ag-cell": {
      display: "flex",
      alignItems: "center",
      border: "none !important",
      borderBottom: "1px solid #E9EAEC !important",
    },
  },
  "& .ag-center-cols-container": {
    minWidth: "100%",
  },
}));

export default DataTable;
