'use client';
import { useMemo } from 'react';

import Pagination from '@/components/Pagination';
import { DataTable } from '@/components/DataTable/TableCustom';

import { cn } from '@/lib/utils';
import { getSocialAudienceColumns } from '@/utils/column';
import { TDataPreview } from '@/utils/SocialData';
import { useRouter, useSearchParams } from 'next/navigation';

const TablePreview = ({ data, isDataset }: {data: TDataPreview, isDataset?: boolean}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const memoizedColumns = useMemo(() => getSocialAudienceColumns(), [isDataset]);

  return (
    <>
      <DataTable
        columns={memoizedColumns}
        data={data.data.items || []}
        loading={data.loading}
        classTable={cn('mt-6 min-h-[930px]', data.total > 0 && !data.loading && 'min-h-fit')}
      />
      <Pagination
        className="mt-4"
        totalCount={data.data.count}
        pageSize={10}
        currentPage={Number(searchParams.get('page') || '1')}
        onPageChange={(page) => {
          const params = new URLSearchParams(searchParams.toString());
          params.set('page', page.toString());
          router.push(`?${params.toString()}`, { scroll: false });
        }}
      />
    </>
  );
};

export default TablePreview;
