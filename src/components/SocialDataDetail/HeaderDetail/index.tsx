'use client';
import useAvatar from '@/hook/useAvatar';
import { formatPayloadAvatar } from '@/utils/utils';
import HeaderDetailModal from '@/components/HeaderDetailModal';
import React from 'react';

interface Props {
  summarize: any;
}

export const HeaderDetail: React.FC<Props> = ({ ...props }: Props) => {
  const { summarize } = props;
  const { avatar } = useAvatar(
    formatPayloadAvatar(summarize?.type || 1, summarize?.fb_uid ?? '', summarize?.actor_id ?? '')
  );
  return (
    <HeaderDetailModal
      name={summarize?.name || ''}
      loading={false}
      avatarUrl={avatar.url}
      size={summarize?.size}
      description={summarize?.description}
      uid={summarize?.fb_uid}
      packageValue={summarize?.package || ''}
      typeAudience={summarize?.type || 1}
      subTypeAudience={summarize?.subtype}
      className={summarize?.description ? 'items-start' : 'items-center'}
    />
  );
};
