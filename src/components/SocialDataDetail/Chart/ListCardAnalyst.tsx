import { RiGroupLine, RiHeartsLine, RiMailStarLine, RiOpenArmLine } from '@remixicon/react';

import AnalystCard from './AnalystCard';
import { cn } from '@/lib/utils';
import { Overview } from '@/utils/ResponseApi';

type Props = {
  data: Overview;
  className?: string;
};

const ListCardAnalyst = ({ className, data }: Props) =>
  data && (
    <div
      className={cn(
        "grid grid-cols-2 max-md:gap-4 md:gap-2 sm:gap-[28px] lg:grid-cols-2 xl:grid-cols-4 w-full",
        className
      )}
    >
      <AnalystCard
        className="bg-[#924FE8]"
        icon={
          <div className="p-[13px] bg-white shadow-xs rounded-md text-primary">
            <RiGroupLine color="#6B0EAC" size={24} />
          </div>
        }
        total={data?.c_phone || 0}
        title={"Total Profile"}
      />
      <AnalystCard
        className="text-primary"
        icon={
          <div className="p-[13px] bg-[#0A0F290A] shadow-xs rounded-md">
            <RiMailStarLine size={24} color="#515667" />
          </div>
        }
        titleColor="text-secondary"
        total={data.c_email || 0}
        title={"Email"}
      />
      <AnalystCard
        className="text-primary"
        titleColor="text-secondary"
        icon={
          <div className="p-[13px] bg-[#0A0F290A] shadow-xs rounded-md">
            <RiOpenArmLine size={24} color="#515667" />
          </div>
        }
        total={data.c_dob || 0}
        title={"Date of Birth"}
      />
      <AnalystCard
        className="bg-white text-primary"
        icon={
          <div className="p-[13px] bg-[#0A0F290A] shadow-xs rounded-md">
            <RiHeartsLine color="#515667" size={24} />
          </div>
        }
        total={data.c_relationships || 0}
        title={"Relationships"}
        titleColor="text-secondary"
      />
    </div>
  );

export default ListCardAnalyst;
