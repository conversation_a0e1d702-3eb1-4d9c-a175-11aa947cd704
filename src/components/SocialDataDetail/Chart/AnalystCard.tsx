'use client'
import CountUp from "react-countup";
import React from "react";
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import useResponsive from '@/hook/useResponsive';

type Props = {
  className?: string;
  total: number;
  icon: React.ReactNode;
  title: string;
  titleColor?: string;
};

const AnalystCard: React.FC<Props> = ({ className, title, total, icon, titleColor }) => {
  const { width } = useResponsive();

  const handleScaleDownText = () => {
    const isBilTotal = total > 100_000_000;
    return width < 1366 && width >= 1280 ?
      isBilTotal ? "text-base" : "text-lg" : width < 576 ? "max-md:text-lg md:text-xl" : isBilTotal ? "text-base" : "text-xl";
  };

  return (
    <div
      className={cn(
        "rounded-2xl shadow-chart p-6 gap-4 flex max-md:flex-col max-md:items-start md:items-center text-white",
        className
      )}
    >
      {icon}
      <div className="flex flex-col flex-1 h-[50px]">
        {false! ? (
          <>
            <Skeleton className="h-[12px] w-1/3" />
            <Skeleton className="h-9 w-2/3 mt-1" />
          </>
        ) : (
          <>
            <span className={cn("text-xs font-semibold", titleColor)}>{title}</span>
            <p
              className={cn("font-semibold transition-all duration-300",
                handleScaleDownText()
              )}
            >
              <CountUp
                className="whitespace-nowrap"
                start={0}
                end={total}
                duration={2.75}
                separator=","
              />
            </p>
          </>
        )}
      </div>
    </div>
  );
};
export default AnalystCard;
