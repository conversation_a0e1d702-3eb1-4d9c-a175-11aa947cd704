'use client';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import ChartWrapper from '@/components/chart/ChartWrapper';
import Barchart from '@/components/chart/BarChart';
import ViewAllIcon from '@/assets/icon/ViewAllIcon';
import { DEFAULT_LABELS_GENDER } from '@/utils/constant';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
import { ChartJSOrUndefined } from 'react-chartjs-2/dist/types';

type Props = {
  labels?: string[];
  data: { label: string; values: number[]; backgroundColor?: string; icon?: any }[];
  loading?: boolean;
  targetLabel?: string[];
};

const AgeChart: React.FC<Props> = ({ labels, data, loading, targetLabel }) => {
  const [showGender, setShowGender] = useState<string[]>(data.map((item) => item.label));

  const barRef = useRef<ChartJSOrUndefined<"bar", number[], string>>(null);
  const mainLabels = useMemo(() => labels || DEFAULT_LABELS_GENDER, [labels]);

  const dataFiltered = useMemo(() => {
    return (data || []).map((item) => {
      let targetIndex = 0;
      const newItem: { label: string; values: number[]; backgroundColor?: string; icon?: any } = {
        label: item.label,
        backgroundColor: item.backgroundColor,
        values: [],
      };

      DEFAULT_LABELS_GENDER.forEach((label) => {
        if (targetLabel?.includes(label)) {
          newItem.values.push(item.values[targetIndex]);
          targetIndex++;
        } else {
          newItem.values.push(0);
        }
      });
      return newItem;
    });
  }, [data, targetLabel]);
  const targetData = targetLabel ? dataFiltered : data;
  const total = useMemo(() => {
    const totals = [];
    for (let i = 0; i < mainLabels.length; i++) {
      let sum = 0;
      for (let j = 0; j < targetData.length; j++) {
        sum += targetData[j].values[i];
      }
      totals.push(sum);
    }
    return totals;
  }, [data]);

  const viewAllBtn = useMemo(
    () => ({
      name: "View all",
      icon: ViewAllIcon,
      currentColor: "#5314A3",
    }),
    []
  );
  const changeShowStack = (index: number) => {
    const chart = barRef.current;
    if (chart) {
      const visibility = chart.getDatasetMeta(index).hidden;
      chart.getDatasetMeta(index).hidden =
        visibility === null ? chart.isDatasetVisible(index) : !visibility;
      chart.update();
    }
  };
  useEffect(() => {
    const chart = barRef.current;
    if (!chart || showGender.includes("View all")) return;
    data.forEach((item, index) => {
      if (!showGender.includes(item.label)) {
        chart.setDatasetVisibility(index, false);
      } else {
        chart.setDatasetVisibility(index, true);
      }
      chart.update();
    });
  }, [showGender]);

  useEffect(() => {
    if (showGender.length === 0) setShowGender(["View all"]);
    if (barRef.current?.getVisibleDatasetCount() === 0) setShowGender(["View all"]);
  }, [showGender]);

  const handleSelected = useCallback((input: string) => {
    setShowGender((prev) => {
      if (input === "View all") {
        return prev.includes("View all") ? [...data.map((item) => item.label)] : ["View all"];
      } else {
        const updatedSelection = prev.includes(input)
          ? prev.filter((item) => item !== input)
          : [...prev, input];
        return updatedSelection.filter((item) => item !== "View all");
      }
    });
  }, []);

  const chartData = useMemo(() => {
    return !showGender.includes("View all")
      ? targetData
      : [
        {
          label: "View all",
          values: total,
          backgroundColor: "#5314A3",
        },
      ];
  }, [data, total, showGender]);

  return (
    <ChartWrapper
      isEmptyData={total.reduce((accumulator, currentValue) => accumulator + currentValue, 0) === 0}
      loading={loading}
      contentRight={
        <div className="ml-auto flex items-center gap-2 flex-wrap justify-end">
          {data.map((item, index) => (
            <button
              style={{
                backgroundColor: showGender.includes(item.label) ? item.backgroundColor : "white",
              }}
              onClick={() => {
                handleSelected(item.label);
                changeShowStack(index);
              }}
              className={cn(
                "border px-2 py-1 shadow-btn h-6 rounded-md whitespace-nowrap flex items-center gap-1 text-xs font-medium",

                showGender.includes(item.label) && `text-white`
              )}
              key={item.label}
            >
              <item.icon color={!showGender.includes(item.label) ? "#0F132499" : "white"} />
              {item.label}
            </button>
          ))}
          <button
            style={{
              backgroundColor: showGender.includes(viewAllBtn.name)
                ? viewAllBtn.currentColor
                : "white",
            }}
            onClick={() => {
              handleSelected(viewAllBtn.name);
              changeShowStack(chartData.length - 1);
            }}
            className={cn(
              "border px-2 py-1 shadow-btn h-6 rounded-md whitespace-nowrap flex items-center gap-1 text-xs font-medium",
              showGender.includes(viewAllBtn.name) && `text-white`
            )}
            key={viewAllBtn.name}
          >
            <viewAllBtn.icon
              color={!showGender.includes(viewAllBtn.name) ? "#0F132499" : "white"}
            />
            {viewAllBtn.name}
          </button>
        </div>
      }
      className={cn("col-span-2")}
      title="Age"
    >
      <Barchart ref={barRef} data={chartData} labels={mainLabels} />
    </ChartWrapper>
  );
};
export default React.memo(AgeChart);
