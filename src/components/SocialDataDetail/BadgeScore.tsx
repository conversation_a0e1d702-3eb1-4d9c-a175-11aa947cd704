'use client'
import { Badge } from "@/components/ui/badge";
import { useEffect, useRef, useState } from "react";
import { cn } from '@/lib/utils';

type BadgeProps = {
  key: string;
  value: number;
};

const BadgeScore: React.FC<{ data: BadgeProps[]; maxWidth: number }> = ({ data, maxWidth }) => {
  const [visibleBadges, setVisibleBadges] = useState<BadgeProps[]>([]);
  const [remainingCount, setRemainingCount] = useState(0);
  const badgeRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const calculateWidths = () => {
      const badgeWidths = badgeRefs.current.map((ref) => ref?.offsetWidth || 0);

      const tempVisible: BadgeProps[] = [];
      let currentWidth = 0;

      //check badgeWidths
      if (badgeWidths.length === 0 || badgeWidths.every((width) => width === 0)) {
        return;
      }

      if (badgeWidths[0] > maxWidth - 50) {
        // Case 1: Show only the first badge and +2
        tempVisible.push(data[0]);
        setRemainingCount(data.length - 1);
      } else if (badgeWidths[0] + (badgeWidths[1] || 0) > maxWidth - 50) {
        // Case 2: Show only the first badge and +2
        tempVisible.push(data[0]);
        setRemainingCount(data.length - 1);
      } else if (badgeWidths[0] + badgeWidths[1] <= maxWidth - 50 && data.length > 2) {
        // Case 3: Show the first two badges and +1
        tempVisible.push(data[0], data[1]);
        setRemainingCount(data.length - 2);
      } else {
        // Case 4: Show all badges if they fit
        for (let i = 0; i < data.length; i++) {
          if (currentWidth + badgeWidths[i] <= maxWidth) {
            tempVisible.push(data[i]);
            currentWidth += badgeWidths[i];
          } else {
            setRemainingCount(data.length - i);
            break;
          }
        }
      }
      setVisibleBadges(tempVisible);
    };

    requestAnimationFrame(calculateWidths);
  }, [data, maxWidth]);

  return (
    <div className="flex gap-1 w-[250px] relative">
      {data.map((badge, idx) => (
        <div
          key={`measure-${idx}`}
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          ref={(el) => (badgeRefs.current[idx] = el)}
          className="invisible opacity-0 absolute pointer-events-none"
        >
          <Badge
            className={cn(
              idx === 0
                ? "bg-gray-600 text-white hover:bg-gray-600"
                : idx === 1
                ? "bg-gray-400 text-white hover:bg-gray-400"
                : "bg-gray-200 text-secondary hover:bg-gray-200"
            )}
          >
            {badge.key}
          </Badge>
        </div>
      ))}
      {visibleBadges.map((badge, idx) => (
        <div key={`measure-container-${idx}`}>
          <Badge
            className={cn(
              "rounded-sm",
              idx === 0
                ? "bg-gray-600 text-white hover:bg-gray-600"
                : idx === 1
                  ? "bg-gray-400 text-white hover:bg-gray-400"
                  : "bg-gray-200 text-secondary hover:bg-gray-200"
            )}
          >
            {badge.key}
          </Badge>
        </div>
      ))}
      {remainingCount > 0 && visibleBadges.length < 3 && (
        <Badge className="whitespace-nowrap badge bg-gray-200 text-secondary hover:bg-gray-200 rounded-sm">
          +{remainingCount}
        </Badge>
      )}
    </div>
  );
};

export default BadgeScore;
