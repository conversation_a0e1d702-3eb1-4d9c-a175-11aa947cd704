type Props = {
  total: number;
};

const ShowResult: React.FC<Props> = ({ total }) => {
  return (
    <div className="flex items-center gap-2">
      <svg
        width="20"
        height="21"
        viewBox="0 0 20 21"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.18269 14.0335L2.93269 17.8604L6.75962 18.6104L10 20.7931L13.2404 18.6104L17.0673 17.8604L17.8173 14.0335L20 10.7931L17.8173 7.55271L17.0673 3.72578L13.2404 2.97578L10 0.793091L6.75962 2.97578L2.93269 3.72578L2.18269 7.55271L0 10.7931L2.18269 14.0335ZM8.85107 14.4453L14.6157 8.94583L13.4652 7.73991L8.85205 12.1409L6.53577 9.92739L5.38429 11.1323L8.85107 14.4453Z"
          fill="#924FE8"
        />
      </svg>
      Result
      <span className="text-primary font-medium text-sm py-0.5 px-2 rounded-md bg-[#0A0F290A] shadow-xs">
        {total?.toLocaleString()}
      </span>
    </div>
  );
};
export default ShowResult;
