'use client';
import BadgeScore from './BadgeScore';
import { Too<PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import ScoreDetail from './ScoreDetail';
import { useRef, useState } from 'react';
import useOutsideClick from '@/hook/useClickOutSide';
import { AudienceCategories } from '@/utils/constant';
import { getCategoryName } from '@/utils/utils';
import useSWR from 'swr';
import { fetcher } from '@/lib/fetcher';

type Data = {
  [key: string]: number;
};
type TCategory = {
  data: AudienceCategories
}
export default function ScoresColumn({ scores }: {scores: Data, categories?: TCategory}) {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const { data: categories } = useSWR<TCategory>('/api/categories', fetcher);
  const ref = useRef(null);
  const refSecond = useRef(null);

  useOutsideClick(ref, refSecond, () => {
    setIsOpen(false);
  });
  const categoryNames = scores
    ? Object.keys(scores).reduce((acc: any, key: string) => {
      acc[key] = getCategoryName({
        categoryCode: key,
        categories: categories?.data as AudienceCategories
      });
      return acc;
    }, {})
    : null;

  const sortedData = scores
    ? Object.entries(scores).sort(([, a], [, b]) => b - a).map(([key, value]) => ( { key: categoryNames[key], value } ))
    : [];

  return scores ? (
    <TooltipProvider delayDuration={100}>
      <Tooltip open={isOpen} onOpenChange={(open) => setIsOpen(open)}>
        <TooltipTrigger ref={ref} onClick={() => setIsOpen(true)}>
          <BadgeScore data={sortedData} maxWidth={250} />
        </TooltipTrigger>
        <TooltipContent
          ref={refSecond}
          side="bottom"
          sideOffset={5}
          className="z-30  text-sm bg-custom-secondary"
        >
          <ScoreDetail data={sortedData} />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  ) : (
    '--'
  );
}
