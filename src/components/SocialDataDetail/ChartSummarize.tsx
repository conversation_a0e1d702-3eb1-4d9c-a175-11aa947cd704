'use client';
import AgeChartModal from './Chart/AgeChartModal';
import ListCardAnalyst from './Chart/ListCardAnalyst';
import { Summarize } from '@/utils/SocialData';
import { cn } from '@/lib/utils';
import { fNumberToString, getCityData } from '@/utils/utils';
import { CityValue } from '@/utils/constant';
import ChartWrapper from '@/components/chart/ChartWrapper';
import BarChartHorizontal from '@/components/chart/BarChartHorizontal';
import Polar<PERSON>hart from '@/components/chart/PolarChart';
import { formatRelationShipsData } from '@/utils/formatRelationShips';
import { Tooltip } from 'chart.js';

interface Props {
  data: Summarize;
  loading: boolean;
  isShowListCard?: boolean;
}

Tooltip.positioners.nearest = function(_, eventPosition) {
  return {
    x: eventPosition.x,
    y: eventPosition.y
  };
};

const ChartSummarize = ({ data, loading, isShowListCard = true }: Props) => {

  const cityData = data.city && getCityData(data.city, 'social');
  const relationships = formatRelationShipsData(data.relationships);

  return (
    <>
      {isShowListCard && <ListCardAnalyst data={data.overview} />}
      <div
        className={cn(
          'grid grid-cols-1 my-6 gap-x-7 gap-y-6 xl:grid-cols-3', 'md:grid-cols-2'
        )}
      >
        <AgeChartModal ageGenderData={data.age_gender || {}} />
        <ChartWrapper
          loading={loading}
          className={cn('col-span-2 lg:col-span-1', 'md:col-span-2')}
          title="Gender"
          isEmptyData={data.gender == undefined || Object.keys(data.gender).length == 0}
        >
          <PolarChart
            labels={['Female', 'Male']}
            values={[data.gender?.F ?? 0, data.gender?.M ?? 0]}
            showTotal={true}
            backgroundColors={['#924FE8', '#F48E2F']}
            titleTooltip="Gender"
          />
        </ChartWrapper>
        <ChartWrapper
          loading={loading}
          className={cn('col-span-2 lg:col-span-1 xl:col-span-2', 'md:col-span-2')}
          title="Top cities"
          isEmptyData={!cityData || cityData?.length == 0}
        >
          <BarChartHorizontal
            values={cityData?.map((item: CityValue) => item.count) || []}
            labels={cityData?.map((item: CityValue) => item.city) || []}
          />
        </ChartWrapper>
        <ChartWrapper
          loading={loading}
          className={cn('col-span-2 xl:col-span-1', 'md:col-span-2')}
          title="Relationships"
          isEmptyData={relationships.total == 0}
        >
          <PolarChart
            labels={relationships.labels}
            values={[...relationships.value]}
            showTotal={true}
            titleTooltip="Relationships"
            isCustomTooltip={true}
            customTooltip={CustomOtherTooltip(relationships.otherItems, relationships.total)}
          />
        </ChartWrapper>
      </div>
    </>
  );
};

export default ChartSummarize;

interface otherItems {
  label: string;
  value: number;
}

const CustomOtherTooltip = (items: otherItems[], total: number) => `
 ${items.map(
  (item) =>
    `<div class="flex gap-1 justify-start w-[250px]">
            <div class= "font-medium text-xs text-secondary 3xl:text-md ml-1 capitalize">${
      item.label
    }:</div>
            <div class = "font-medium text-xs text-primary 3xl:text-md">${
      fNumberToString(item.value) + ' - ' + ( ( item.value / total ) * 100 ).toFixed(2)
    }%</div>
        </div>`
).join('')}
`;
