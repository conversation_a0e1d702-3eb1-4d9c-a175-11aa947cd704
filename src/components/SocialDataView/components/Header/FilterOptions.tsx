'use client';
import { RiDeleteBin6Line, RiFilter2Line } from '@remixicon/react';
import omit from 'lodash/omit';

import { cn } from '@/lib/utils';
import { AudienceCategories, TSocialFilterProps } from '@/utils/constant';
import filterOptions from '@/utils/filterOption';
import MultiSelect from '@/components/MSelect';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import useSWR from 'swr';
import { Box } from '@/components/Box';
import { fetcher } from '@/lib/fetcher';

type TCategory = {
  data: AudienceCategories
}

const FilterOptions = ({
  isFilterPackage,
  isFilterType,
  isFilterCategory,
  className,
  searchParams
}: TSocialFilterProps) => {
  const { data: categories } = useSWR<TCategory>('/api/categories', fetcher);
  const searchParamsUrl = useSearchParams();
  const [selectedOption, setSelectedOption] = useState<{[key: string]: any}>({});

  useEffect(() => {
    if (searchParams) {
      setSelectedOption(searchParams);
    }
  }, [searchParams]);

  const router = useRouter();

  if (!categories) {
    return (
      <div className="flex items-center justify-center h-20">
        <span>Loading filters...</span>
      </div>
    );
  }

  const handleChange = (key: string, value: {label: string; value: string}[]) => {
    const newValue = value.map((item: {label: string; value: string}) => item?.value).join(',');
    setSelectedOption((prev) => ( { ...prev, [key]: newValue ? newValue : undefined } ));
  };

  const filters = filterOptions({
    isFilterCategory,
    isFilterType,
    isFilterPackage,
    params: searchParams,
    categories: categories.data
  });

  const renderFilterOptions = () => {
    return filters.map((filter) => {
      let paramsDefault;
      if (filter?.key) {
        const paramsFilter = ( selectedOption?.[filter?.key] || '' ).split(',');
        paramsDefault = ( filter.options || [] ).filter((x) => paramsFilter.indexOf(x?.value) !== -1);
      }
      switch (filter?.type) {
        case 'select':
          return (
            <MultiSelect
              key={filter.key}
              defaultValue={paramsDefault}
              options={filter.options}
              placeholder={filter.placeholder}
              onChange={(value: {label: string; value: string}[]) => {
                handleChange(filter.key, value);
              }}
              isMulti
            />
          );
        default:
          return null;
      }
    });
  };

  return (
    <div
      className={cn(
        'rounded-xl max-lg:mt-6 max-md:shadow-sm transition-all duration-300 max-md:h-[256px]'
      )}
    >
      <Box className="flex min-md:hidden items-center py-[10px] px-2 ">
        <div className="flex gap-1 font-semibold text-sm text-[#515667]">
          <RiFilter2Line size={20} color={'#515667'} className="mr-1 block xl:hidden" />
          <span>Filter</span>
        </div>
      </Box>
      <div className={cn('flex items-center gap-3 max-md:p-2 min-lg:mt-6', className)}>
        {renderFilterOptions()}
        <Box className="gap-3 items-center h-10 max-md:flex-row-reverse max-md:w-full">
          <button
            className="max-lg:flex-1 font-semibold h-10 bg-primary text-white flex items-center p-[5px_10px] max-md:w-full max-xl:justify-center my-auto text-sm rounded-2xl cursor-pointer"
            onClick={() => {
              const params = new URLSearchParams(searchParamsUrl);
              Object.entries(selectedOption).forEach(([key, value]) => {
                if (value !== undefined) {
                  params.set(key, String(value));
                }
              });
              if (!params.has('q')) {
                params.set('q', '');
              }
              if (!params.has('page')) {
                params.set('page', '1');
              }
              router.push(`?${params.toString()}`, { scroll: false });
            }}
          >
            <RiFilter2Line size={17} className="mr-1 hidden min-md:block" />
            <span>Filter</span>
          </button>
          {Object.keys(omit(searchParams, ['page', 'q'])).length > 0 && (
            <button
              onClick={() => {
                router.push('?q=&page=1', { scroll: false });
                setSelectedOption({});
              }}
              className="max-lg:flex-1 text-sm font-semibold text-red-400 flex items-center gap-0.5 my-auto hover:text-red-500 max-xl:justify-center cursor-pointer"
            >
              <RiDeleteBin6Line size={16} />
              Clear
            </button>
          )}
        </Box>
      </div>
    </div>
  );
};

export default FilterOptions;
