import LeftHeader from './LeftHeader';
import { RightHeader } from './RightHeader';
import FilterOptions from './FilterOptions';
import { cn } from '@/lib/utils';
import SearchModal from '@/components/SocialDataView/components/SearchModal';

interface Props {
  leftChildren?: {
    title?: string;
    highlight?: string | undefined;
    titleSize?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
    subTitle?: string;
  };

  rightChildren?: React.ReactNode;
  className?: string;
  isFilterType?: boolean;
  isFilterCategory?: boolean;
  isFilterPackage?: boolean;
  isShowFilter?: boolean;
  isShowSearch?: boolean;
  searchParams?: {[key: string]: string | string[] | undefined};
}

const HeaderWrapper = ({ rightChildren, leftChildren, searchParams, ...props }: Props) => {
  const { title, titleSize, subTitle, highlight } = leftChildren || {};
  const { isShowFilter, isShowSearch } = props;
  return (
    <>
      <div className={cn('flex items-center md:items-start flex-wrap mt-3', props.className)}>
        <LeftHeader title={title} highlight={highlight} titleSize={titleSize} subTitle={subTitle} />
        <RightHeader rightChildren={rightChildren} />
      </div>
      {isShowSearch&& <SearchModal />}
      {isShowFilter && <FilterOptions {...props} searchParams={searchParams} />}
    </>
  );
};

export default HeaderWrapper;
