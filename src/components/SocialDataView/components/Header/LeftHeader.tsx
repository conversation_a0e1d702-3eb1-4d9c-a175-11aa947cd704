import { cn } from '@/lib/utils';

interface Props {
  title?: string;
  highlight?: string;
  titleSize?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  subTitle?: string;
  filter?: boolean;
  filterChip?: boolean;
  leftChildren?: React.ReactNode;
  handleFunction?: () => void;
}

const LeftHeader = (props: Props) => {
  const { title, subTitle, leftChildren, titleSize, highlight } = props;
  return (
    <div className="flex-1">
      {title && (
        <div
          className={cn('text-primary flex flex-col md:flex-row', titleSize ?
            `text-${titleSize}` :
            'text-[24px]')}
        >
          <span className="font-semibold">{title}</span>
          {highlight && (
            <span className="font-semibold md:ml-2 bg-gradient-to-r from-[#A33AD8] via-[#8347CE] to-[#5138B9] inline-block text-transparent bg-clip-text">
              {highlight}
            </span>
          )}
        </div>
      )}
      {subTitle && <div className="text-secondary text-md mt-2">{subTitle}</div>}

      <div>{leftChildren}</div>
    </div>
  );
};

export default LeftHeader;
