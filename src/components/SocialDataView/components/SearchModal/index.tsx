'use client';
import { useEffect, useRef, useState } from 'react';
import { RiCloseFill, RiSearchLine } from '@remixicon/react';

// import SearchSuggest from './SearchSuggest';
import useOutsideClick from '@/hook/useClickOutSide';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
// import useDebounce from '@/hook/useDebounce';
import { useRouter, useSearchParams } from 'next/navigation';

const SearchModal = ({}: {
  keywordSuggest?: boolean;
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryTerm = searchParams.get('q') || '';
  const [searchTerm, setSearchTerm] = useState<string>('');
  // const [searchSuggest, setSearchSuggest] = useState<{
  //   fb_uid: string,
  //   keywords: string,
  //   name: string
  // }[]>([]);
  const [isOpenSearchSuggest, setIsOpenSearchSuggest] = useState<boolean>(false);

  const ref = useRef<HTMLInputElement | null>(null);
  const refSecond = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setSearchTerm(queryTerm);
  }, [queryTerm]);

  // const debounceSearch = useDebounce(searchTerm, 500);
  const handleAction = () => {
    const params = new URLSearchParams(searchParams);
    params.set('q', searchTerm);
    params.set('page', '1');
    router.push(`?${params.toString()}`, { scroll: false });
    //remove focus input
    if (ref.current) {
      ref.current.blur();
    }
    //close search suggest
    setIsOpenSearchSuggest(false);
  };

  const handleOnkeyUpEnter = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.keyCode === 13) {
      handleAction();
    }
  };
  const onReset = () => {
    const params = new URLSearchParams(searchParams);
    params.set('q', '');
    router.push(`?${params.toString()}`, { scroll: false });
  };

  const onFocus = () => {
    setIsOpenSearchSuggest(true);
    //
    // if (searchTerm == '') {
    //   fetchDataSuggest('');
    // }
  };

  useOutsideClick(ref, refSecond, () => {
    setIsOpenSearchSuggest(false);
  });

  // const fetchDataSuggest = useCallback(
  //   async (q: string) => {
  //     const res = await fetch(`/api/search-suggest?q=${q}`);
  //     const data = await res.json();
  //
  //     if (data.data) {
  //
  //       setSearchSuggest(data.data.items);
  //     }
  //   },
  //   [debounceSearch]
  // );
  //
  // useEffect(() => {
  //   fetchDataSuggest(debounceSearch.trim()).finally(()=>{});
  // }, [debounceSearch]);

  return (
    <div className={cn('relative mt-6', isOpenSearchSuggest && 'bg-white z-[9999] rounded-xl')}>
      <div
        className={cn(
          'border border-[#DEE0E3] rounded-[12px] flex items-center focus:shadow-focus focus:border-focus',
          isOpenSearchSuggest && 'border border-[#924FE8]'
        )}
      >
        <div className="flex-1 px-[12px] py-[10px] flex items-center gap-2">
          <span className="cursor-pointer" onClick={() => handleAction()}>
            <RiSearchLine opacity={0.4} color="#0F1324" size={16} />
          </span>
          <input
            className="w-full font-light bg-transparent focus:outline-none text-sm text-primary"
            placeholder={'Search for people, fanpage, group, and more...'}
            onKeyUp={(e) => handleOnkeyUpEnter(e)}
            onChange={(event) => setSearchTerm(event.target.value)}
            onFocus={() => onFocus()}
            value={searchTerm || ''}
            ref={ref}
          />
          {searchTerm && (
            <div className="flex items-center gap-1">
              <Badge
                className="bg-transparent hover:bg-transparent cursor-pointer"
                onClick={onReset}
              >
                <RiCloseFill color="#14151A" />
              </Badge>
            </div>
          )}
        </div>
      </div>
      {/*{isOpenSearchSuggest && <SearchSuggest suggestData={searchSuggest} refSecond={refSecond} />}*/}
    </div>
  );
};

export default SearchModal;
