import { RiSearchLine } from "@remixicon/react";
import Link from 'next/link';

export interface ISuggestSearchItem {
  fb_uid: string;
  keywords: string;
  name: string;
}
const SearchSuggest = ({
  suggestData,
  refSecond,
}: {
  suggestData: ISuggestSearchItem[];
  refSecond: React.RefObject<HTMLInputElement | null>;
}) => {
  return (
    <div
      ref={refSecond}
      className="absolute w-full z-50 min-h-[250px] pl-[6px] border border-[#924FE8] rounded-xl shadow-sm p-2 bg-white -bottom-1 translate-y-full"
    >
      {!!suggestData.length && (
        <span className="text-tertiary text-sm pl-[6px]">Try searching for</span>
      )}
      <div>
        {suggestData.length ? (
          suggestData.map((suggest: ISuggestSearchItem) => {
            const { fb_uid, name } = suggest;

            return (
              <div key={fb_uid} className="flex items-center gap-2 px-[6px] py-2 text-primary text-sm hover:bg-[#0A0F290A] hover:rounded-md">
                <RiSearchLine opacity={0.4} color="#0F1324" size={16} />
                <Link
                  prefetch={true}
                  href={`/dataset-library/detail/${fb_uid}`}
                  className="capitalize"
                >
                  {name.toLocaleLowerCase()}
                </Link>
              </div>
            );
          })
        ) : (
          <div className="text-center">There are no matches</div>
        )}
      </div>
    </div>
  );
};

export default SearchSuggest;
