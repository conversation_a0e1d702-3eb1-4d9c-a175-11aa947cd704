'use client'
import DataTable from "@/components/DataTable";
import HeaderWrapper from "./Header";
import { cn } from '@/lib/utils';
import { TRENDING_COLUMN } from '@/utils/column';

const TableTrending = ({ isShow, data }: { isShow: boolean,data: any[] }) => {
  return (
    <>
      <div className={cn(isShow && "hidden", "mt-10")}>
        <HeaderWrapper leftChildren={{ title: "Audiences Trending", titleSize: "lg" }} />
        <DataTable
          column={TRENDING_COLUMN}
          rowData={data}
          pagination={false}
          className=" border-none"
          loading={false}
        />
      </div>
    </>
  );
};

export default TableTrending;
