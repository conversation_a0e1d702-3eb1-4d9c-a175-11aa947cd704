import { RiArrowDownLine } from "@remixicon/react";
import { cn } from '@/lib/utils';

const ViewMore = ({
  isShowMore,
  count,
  setIsShowMore,
}: {
  isShowMore: boolean;
  count: number;
  setIsShowMore: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const showALl = count <= 8;

  return (
    count > 0 && (
      <div
        className="flex items-center gap-1 border border-[#C8CAD0] rounded-xl p-[6px_10px_6px_8px] shadow-xs cursor-pointer"
        onClick={() => setIsShowMore(!isShowMore)}
      >
        <div className="bg-[#E9EAEC] border-[1px] p-[2px_3px] border-[#0A0F2914] text-secondary text-xs rounded-[6px] hover:bg-[#E9EAEC]">
          {isShowMore ? count : count > 8 ? 8 : count}
        </div>

        <span className={cn("text-primary text-sm")}>
          {showALl ? "All" : isShowMore ? 'View less' : 'View more'}
        </span>

        {!showALl && (
          <RiArrowDownLine
            size={16}
            color="#14151A"
            className={cn("duration-700", isShowMore ? "rotate-180 " : "rotate-0")}
          />
        )}
      </div>
    )
  );
};

export default ViewMore;
