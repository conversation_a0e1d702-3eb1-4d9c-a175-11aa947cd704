'use client'
import isArray from 'lodash/isArray';
import range from 'lodash/range';
import CategoryItem from './CategoryItem';
import { cn } from '@/lib/utils';
import { AudienceCategories, ICategoryItem, TCategoriesResponse } from '@/utils/constant';
import { Skeleton } from '@/components/ui/skeleton';
import HeaderWrapper from '@/components/SocialDataView/components/Header';
import { map } from 'lodash';
import { useState } from 'react';
import ViewMore from '@/components/SocialDataView/components/Categories/ViewMore';
import useSWR from 'swr';
import { fetcher } from '@/lib/fetcher';

interface Props {
  loading: boolean;
  isShow: boolean;
  searchParams: {[key: string]: string | string[] | undefined}
  isShowFilter?: boolean;
}
type TCategory = {
  data: AudienceCategories
}
const CategoriesWrapper =  (props: Props) => {
  const { data: categoryData } = useSWR<TCategory>('/api/categories', fetcher);
  const { loading, searchParams, isShowFilter } = props;
  const [isShowMore, setIsShowMore] = useState<boolean>(false);
  const data = {
    count: categoryData?.data?.count ?? 0,
    items: categoryData?.data?.items ?? []
  };

  const renderCategory = () => {
    const unCategory = data.items.find((item) => item.name === 'Uncategory');
    const sortedCategory = data.items.filter((item) => item.name !== 'Uncategory').sort((a, b) => b.audience_count - a.audience_count).concat(unCategory ?
      [unCategory] :
      []);
    const dataToRender = isShowMore ? sortedCategory : sortedCategory.slice(0, 8);
    if (loading) {
      return range(0, 4).map((index: number) => (
        <div key={index}>
          <Skeleton className="h-[125px] w-full rounded-xl" />
          <Skeleton className="h-4 w-1/2 my-4" />
          <Skeleton className="h-4 w-1/3" />
        </div>
      ));
    }

    return map(dataToRender, (category: ICategoryItem) => (
      <CategoryItem key={category.name} {...category} {...props} />
    ));
  };

  const isShowCategory = isArray(data.items) && data.items.length > 0;

  return (
    <div className={cn((props.isShow || (!loading && !isShowCategory)) && "hidden")}>
      <HeaderWrapper
        searchParams={searchParams}
        leftChildren={{ title: "Categories", titleSize: "lg" }}
        className="md:items-center"
        isShowFilter={isShowFilter}
        rightChildren={
          <ViewMore
            count={data.items.length || 0}
            isShowMore={isShowMore}
            setIsShowMore={setIsShowMore}
          />
        }
      />
      <div className="grid grid-cols-2 lg:grid-cols-4 md:grid-cols-3 gap-4 md:gap-10 lg:gap-12 mt-4">
        {renderCategory()}
      </div>
    </div>
  );
};

export default CategoriesWrapper;
