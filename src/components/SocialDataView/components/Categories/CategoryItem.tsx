import { ICategoryItem } from '@/utils/constant';
import { fNumberToString } from '@/utils/utils';
import Link from 'next/link';
import Image from 'next/image';

const CategoryItem = ({
  code,
  img_cover,
  audience_count,
  name,
}: ICategoryItem ) => {
  const params = new URLSearchParams();
  params.set('category__in', String(code));
  return (
    <Link prefetch={true} href={`/dataset-library?${params.toString()}`} key={code}>
      <div className="relative mb-3" >
        {img_cover ?
          <Image quality={100} src={img_cover} width={177} height={177} className="w-full h-[177px] object-cover rounded-2xl cursor-pointer" alt={img_cover} />
          :
          <div className="w-full h-[177px] object-cover rounded-2xl cursor-pointer"></div>}
        <OverLayWrapper />
        <h2 className="absolute bottom-[11px] left-[23px] z-[1] text-white text-lg font-semibold mt-4 cursor-pointer">
          {name}
        </h2>
      </div>
      <span className="text-md font-semibold">
        <span className="text-primary mr-1">+ {fNumberToString(audience_count)}</span>
        <span className="text-secondary">Audiences</span>
      </span>
    </Link>
  );
};

export default CategoryItem;

const OverLayWrapper = () => (
  <div className="absolute rounded-2xl h-full w-full z-[1] top-0 left-0 bg-gradient-to-b from-transparent via-black/5 via-black/10 to-black/70" />
);
