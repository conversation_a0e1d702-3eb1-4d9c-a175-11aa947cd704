'use client';

import { Badge } from '@/components/ui/badge';
import DataTable from '@/components/DataTable';
import { cn } from '@/lib/utils';
import { fNumberToString } from '@/utils/utils';
import { SOCIAL_AUDIENCES_COLUMN } from '@/utils/column';
import Pagination from '@/components/Pagination';
import { SortType, TSocialDataResponse } from '@/utils/constant';
import { useSearchParams, useRouter } from 'next/navigation';

interface Props {
  dataSuggest: TSocialDataResponse | null | undefined;
}

const SearchAndFilterResult = ({ ...props }: Props) => {
  const { dataSuggest } = props;
  const router = useRouter();
  const searchParams = useSearchParams();

  const data = {
    data: dataSuggest?.items ?? [],
    count: dataSuggest?.count ?? 0
  };

  const handleSortChange = (sortType: SortType, field: string) => {
    const sortBy = {
      asc: `${field}`,
      desc: `-${field}`,
    };
    const params = new URLSearchParams(searchParams);
    params.set('order_by', sortBy[sortType])
    router.push(`?${params.toString()}`, { scroll: false });
  };

  return (
    <div className={cn('bg-white transition-transform duration-700 z-50')}>
      <div className="flex items-end justify-between mt-2 pt-5 w-fit text-[#8f5cff] pb-1 border-b-[2px] border-[#914fe8] gap-2">
        Result
        <Badge className="bg-[#0A0F290A] text-[#924FE8] rounded-sm hover:bg-[#0A0F290A] shadow-sm ">
          {fNumberToString(data.count)}
        </Badge>
      </div>
      <DataTable
        rowData={data.data}
        column={SOCIAL_AUDIENCES_COLUMN({ handleSortChange })}
        pagination
        className="h-[700px] mt-2 mb-0"
        loading={false}
      />
      {/* Limit 50 page */}
      <Pagination
        className="mt-4"
        currentPage={Number(searchParams.get('page') || '1')}
        pageSize={10}
        totalCount={data.count < 500 ? data.count : 500}
        onPageChange={(page) => {
          const params = new URLSearchParams(searchParams.toString());
          params.set('page', page.toString());
          router.push(`?${params.toString()}`, { scroll: false });
        }}
      />
    </div>
  );
};

export default SearchAndFilterResult;
