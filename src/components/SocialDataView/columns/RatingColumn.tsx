import { TooltipPortal } from "@radix-ui/react-tooltip";
import { ICellRendererParams } from "ag-grid-community";
import { Box } from '@/components/Box';
import StarRating from '@/assets/icon/StartIcon';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const RatingColumn = (params: ICellRendererParams | any) => {
  const { rating } = params.data || params;

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger>
          <Box className="gap-1">
            <StarRating rating={rating??0} />
          </Box>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent >
            Rating: {rating?.toFixed(1)}
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
};

export default RatingColumn;
