import { ICellRendererParams } from "ag-grid-community";
import { SUB_TYPE_SOCIAL_LABEL, TypeOptions } from '@/utils/constant';

const TypeColumn = (params: ICellRendererParams | any) => {
  const { type, subtype } = params.data || params;

  const LABEL_TYPE = subtype
    ? SUB_TYPE_SOCIAL_LABEL[subtype - 1].name
    : (TypeOptions.find((option) => option.id == type)?.name as string);
  return <div className="text-secondary font-medium">{LABEL_TYPE}</div>;
};

export default TypeColumn;
