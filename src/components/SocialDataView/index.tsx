import HeaderWrapper from './components/Header';
import TableTrending from './components/TableTrending';
import CategoriesWrapper from '@/components/SocialDataView/components/Categories';
import { TSocialDataResponse } from '@/utils/constant';
import SearchAndFilterResult from '@/components/SocialDataView/components/SearchAndFilterResult';
export interface ParamsProps {
  limit?: string;
  page?: string;
  category__in?: string;
  type__in?: string;
  package__in?: string;
  q?: string;
  order_by?: string;

  [key: string]: unknown;
}

const SocialDataView = ({ data, searchParams }: {
  data: {
    trending: TSocialDataResponse | null;
    suggest: TSocialDataResponse | null | undefined
  }, searchParams: {[key: string]: string | string[] | undefined}
}) => {
 const isHaveSearch = !!searchParams?.q || !!searchParams?.page
  return (
    <>
      <HeaderWrapper
        searchParams={searchParams}
        isFilterType
        isFilterCategory
        isFilterPackage
        isShowSearch
        isShowFilter
        className="flex-col items-start md:flex-row"
        leftChildren={{
          title: isHaveSearch ? 'Search for' : 'Social Data',
          highlight: isHaveSearch ? 'anything you want.' : '',
          subTitle: 'Data of Social Audience'
        }}
      />
      {data.suggest && <SearchAndFilterResult dataSuggest={data.suggest} />}
      {!data.suggest && <>
        <CategoriesWrapper
          searchParams={searchParams}
          loading={false}
          isShow={isHaveSearch}
        />
        <TableTrending isShow={false} data={data.trending?.items ?? []} />
      </>}
    </>
  );
};
export default SocialDataView;
