import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

const BadgePackage = ({
  packageValue,
  className,
}: {
  packageValue: string;
  className?: string;
}) => {
  let style = "";

  switch (packageValue && packageValue.toLowerCase()) {
    case "bronze":
      style = "bg-custom-secondary hover:bg-custom-secondary text-secondary";
      break;
    case "silver":
      style = "bg-[#E3EAFD] hover:bg-[#E3EAFD] text-[#133A9A] ";
      break;
    case "gold":
      style = "bg-[#D1FAE4] hover:bg-[#D1FAE4] text-[#166E3F] ";
      break;
    case "platinum":
      style = "bg-[#FDEAD8] hover:bg-[#FDEAD8] text-[#AE590A] ";
      break;
    case "diamond":
      style = "bg-[#FCE5E4] hover:bg-[#FCE5E4] text-[#9A1C13] ";
      break;
    case "titanium":
      style = "bg-[#ECDFFB] hover:bg-[#ECDFFB] text-[#5314A3] ";
      break;
    default:
      <div>{packageValue}</div>;
  }
  return (
    <Badge
      className={cn(
        "rounded-sm capitalize hover:opacity-70 bg-transparent text-primary p-1",
        style,
        className
      )}
    >
      {packageValue || "--"}
    </Badge>
  );
};

export default BadgePackage;
