"use client"
import { Box } from "@/components/Box"
import IconFavicon from "@/assets/icon/IconFavicon"
import LogoSvg from "@/assets/icon/LogoSvg"
import Link from "next/link"
import { APP_DOMAIN } from "@/utils/constant"
import { But<PERSON> } from "@/components/ui/button"
import { RiMenu2Line } from "@remixicon/react"
import { Sheet, <PERSON>et<PERSON>ontent, SheetTitle, SheetTrigger } from '@/components/ui/sheet';

export const Header = () => {
  return (
    <header className="w-full m-auto py-4 shadow-md sticky top-0 z-50 bg-white">
      <div className="max-w-[1156px] mx-auto">
        <Box>
          <Box className="gap-8 relative max-lg:w-full">
            <Link prefetch={true} href={"/"} className="flex items-center justify-center gap-2 max-lg:pl-4">
              <IconFavicon />
              <LogoSvg />
            </Link>

            <div className="hidden lg:flex items-center">
              <a
                href={"/dataset-library"}
                className="h-10 text-sm font-medium leading-4 px-[10px] py-[12px] bg-transparent text-primary rounded-xl"
              >
                Dataset Library
              </a>
              <a
                href={"/#"}
                className="h-10 text-sm font-medium leading-4 px-[10px] py-[12px] bg-transparent text-primary rounded-xl"
              >
                Pricing
              </a>
            </div>

            <Sheet>
              <SheetTrigger asChild className="lg:hidden">
                <Button className="cursor-pointer bg-transparent hover:bg-transparent">
                  <RiMenu2Line color="#14151A" size={20} />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] sm:w-[400px] p-0">
                <SheetTitle className="sr-only"/>
                <div className="flex flex-col h-full py-6">
                  <div className="px-6 flex flex-col space-y-4">
                    <a
                      href={"/dataset-library"}
                      className="h-10 text-sm font-medium leading-4 px-[10px] py-[12px] bg-transparent text-primary rounded-xl"
                    >
                      Dataset Library
                    </a>
                    <a
                      href={"/#"}
                      className="h-10 text-sm font-medium leading-4 px-[10px] py-[12px] bg-transparent text-primary rounded-xl"
                    >
                      Pricing
                    </a>
                  </div>

                  <div className="mt-auto px-6 space-y-4">
                    <a
                      href={APP_DOMAIN + "/login"}
                      className="block text-center border border-[#A7AAB1] h-10 text-sm font-medium leading-4 px-[10px] py-[12px] bg-transparent text-primary rounded-xl"
                    >
                      Login
                    </a>
                    <a
                      href={APP_DOMAIN + "/signup"}
                      className="block text-center h-10 text-sm font-medium leading-4 px-[10px] py-[12px] bg-primary text-[#FDFDFD] rounded-xl"
                    >
                      Sign up
                    </a>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </Box>

          <Box className="gap-4 hidden lg:flex max-xl:pr-4">
            <a
              href={APP_DOMAIN + "/login"}
              className="h-10 text-sm font-medium leading-4 px-[10px] py-[12px] bg-transparent text-primary rounded-xl"
            >
              Login
            </a>
            <a
              href={APP_DOMAIN + "/signup"}
              className="h-10 text-sm font-medium leading-4 px-[10px] py-[12px] bg-primary text-[#FDFDFD] rounded-xl"
            >
              Sign up
            </a>
          </Box>
        </Box>
      </div>
    </header>
  )
}
