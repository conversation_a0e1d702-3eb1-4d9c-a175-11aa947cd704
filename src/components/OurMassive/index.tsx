'use client';
import { Box } from '@/components/Box';
import Image from 'next/image';
import CountUp from 'react-countup';

const massiveData = [

  {
    id: 1,
    count: 12_000_000,
    description: 'Facebook Groups'
  },
  {
    id: 2,
    count: 20_000_000,
    description: 'Facebook Fanpage'
  },
  {
    id: 3,
    count: 81_000_000,
    description: 'Facebook Profiles'
  },
  {
    id: 4,
    count: 13_000_000,
    description: 'Professional Categorized Profiles'
  },
];

export const OurMassive = () => {

  const formatNumber = (value: number): string => {
    if (value >= 1e9) {
      // For billions (e.g., 10,000,000,000 becomes 13B+)
      return ( value / 1e9 ).toFixed(0) + 'B+';
    } else if (value >= 1e6) {
      // For millions (e.g., 10,000,000 becomes 13M+)
      return ( value / 1e6 ).toFixed(0) + 'M+';
    } else if (value >= 1e3) {
      // For thousands (e.g., 10,000 becomes 13K+)
      return ( value / 1e3 ).toFixed(0) + 'K+';
    }
    return value.toString();
  };
  return (
    <div className="w-full bg-[#F0F0F0] py-[40px] mb-[48px]">
      <h2 className="text-[36px] lg:text-[48px] font-semibold text-center mb-[48px]">
        Our Massive Data Coverage
      </h2>
      <div className="max-w-[1156px] mx-[32px] lg:mx-auto">
        <Box className="flex-col lg:flex-row">
          <Image quality={100} src={'/features/profile.png'} alt={'profile'} width={639} height={750} />
          <div className="w-[300px] max-lg:pt-[24px]">
            {massiveData.map((data) => {
                return <div key={data.id} className="lg:pb-[24px] lg:pt-[24px] px-[24px] border-l-[4px] border-[#8F5CFF] mb-[48px]">
                  <CountUp
                    className={'text-primary text-[36px] lg:text-[48px] font-semibold'}
                    start={0}
                    end={data.count}
                    duration={10}
                    formattingFn={formatNumber}
                  />
                  <p className="text-[#515667] text-[16px] text-[18px] font-medium">
                    {data.description}
                  </p>
                </div>;
              }
            )}

          </div>
        </Box>
      </div>
    </div>
  );
};
