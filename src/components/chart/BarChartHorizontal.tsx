'use client';
import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import { ArcElement, BarElement, CategoryScale, Chart, Legend, LinearScale, Title, Tooltip } from 'chart.js';
import { barChartHorizontalPlugins, barChartTooltip, getOrCreateTooltip } from './utils';
import { cn } from '@/lib/utils';

type Props = {
  values: number[];
  backgroundColors?: string;
  labels: string[];
  className?: string;
};
Chart.register(ArcElement, Tooltip, Legend, Title, BarElement, CategoryScale, LinearScale);


const BarChartHorizontal: React.FC<Props> = ({ values, labels, className, backgroundColors }) => {

  const totalValue = values.reduce((acc, cur) => acc + cur, 0);

  const divRef = React.useRef<HTMLDivElement>(null);
  const [scrollPosition, setScrollPosition] = useState({ left: 0, top: 0 });

  useEffect(() => {
    const handleScroll = () => {
      if (divRef.current) {
        setScrollPosition({
          left: divRef.current.scrollLeft,
          top: divRef.current.scrollTop
        });
      }
    };
    const container = divRef.current;
    container?.addEventListener('scroll', handleScroll);

    return () => {
      container?.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div
      ref={divRef}
      className={cn('max-h-[248px] min-h-[248px] h-full w-full overflow-x-hidden', className)}
    >
      <div
        style={{
          height: `${values.length * 50}px`
        }}
      >
        <Bar
          data={{
            labels: labels,
            datasets: [
              {
                indexAxis: 'y',
                data: values,
                backgroundColor: Array(values.length).fill(0).map(() => ( backgroundColors ?
                  backgroundColors :
                  '#924FE8' )),
                borderSkipped: false,
                borderRadius: 100,
                barThickness: 8,
                maxBarThickness: 10,
                barPercentage: 0.8
              }
            ]
          }}
          options={{
            indexAxis: 'y',
            scales: {
              x: {
                offset: false,
                border: {
                  dash: [2, 4],
                  display: false
                },
                stacked: true,
                beginAtZero: true,
                grid: {
                  tickColor: 'transparent'
                },
                display: false,
                grace: '10%'
              },
              y: {
                border: {
                  display: false
                },
                grid: {
                  display: false
                },
                stacked: true,
                display: false
              }
            },
            maintainAspectRatio: false,
            aspectRatio: 2,
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                position: 'nearest',
                enabled: false,
                external(args) {
                  const { chart, tooltip } = args;
                  const tooltipEl = getOrCreateTooltip(chart, 'Top Cities');
                  if (tooltip.opacity === 0) {
                    tooltipEl.style.opacity = 0;
                    return;
                  }
                  if (tooltip.body) {
                    const tableRoot = tooltipEl.querySelector('.chartjs-tooltip > div');
                    const titleLines = tooltip.title || [];
                    const bodyLines = tooltip.body.map((b) => b.lines);
                    const tableHead = document.createElement('div');

                    titleLines.forEach(() => {
                      const tr = document.createElement('div');
                      tableHead.appendChild(tr);
                      const tableBody = document.createElement('div');
                      tableBody.className = 'table-body-tooltips';
                      bodyLines.forEach((body, i) => {
                        const value = body[0].split(':');
                        const colors = tooltip.labelColors[i];
                        const tooltipHtml = barChartTooltip(totalValue, tooltip.title, value[0]);
                        tableBody.innerHTML = tooltipHtml;
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        // @ts-expect-error
                        tableBody.querySelector('.dots-tooltip').style.backgroundColor =
                          colors.backgroundColor;
                      });

                      while (tableRoot.firstChild) {
                        tableRoot.firstChild.remove();
                      }

                      tableRoot.appendChild(tableHead);
                      tableRoot.appendChild(tableBody);
                    });
                    const { offsetLeft: positionX, offsetTop: positionY } = chart.canvas;

                    tooltipEl.style.opacity = '1';
                    tooltipEl.style.left = positionX + tooltip.caretX - scrollPosition.left + 'px';
                    tooltipEl.style.top = positionY + tooltip.caretY - scrollPosition.top + 'px';
                  }
                }
              }
            }
          }}
          plugins={barChartHorizontalPlugins(totalValue, labels, values)}
        />
      </div>
    </div>
  );
};

export default BarChartHorizontal;
