'use client'
import { useMemo, forwardRef, useEffect, useRef } from "react";
import {
  Chart,
  ArcElement,
  Tooltip,
  Legend,
  Title,
  BarElement,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
} from "chart.js";
import { getOrCreateTooltip } from "./utils";
import { removeFormatNumber } from '@/utils/utils';
import dynamic from 'next/dynamic';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
import { ChartJSOrUndefined } from 'react-chartjs-2/dist/types';
const BarChartRender = dynamic(
  () => import('react-chartjs-2').then((mod) => mod.Bar),
  { ssr: false }
);


Chart.register(
  ArcElement,
  Tooltip,
  Legend,
  Title,
  BarElement,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement
);

type Props = {
  labels: string[];
  data: { label: string; values: number[]; backgroundColor?: string }[];
};

// eslint-disable-next-line react/display-name
const Barchart = forwardRef<ChartJSOrUndefined<"bar", number[], string>, Props>(
  ({ data, labels }, ref) => {

    const total = useMemo(() => {
      return data.map((item) => item.values.reduce((acc, cur) => acc + cur, 0));
    }, [data]);

    const totalSum = useMemo(() => {
      return total.reduce((acc, cur) => acc + cur, 0);
    }, [total]);

    const step = useMemo(() => {
      const init = total.filter((item) => item !== 0);
      const checked = data.map((item) => {
        const itemCheck = item.values
          .map((value) => {
            if (value / init[0] === 1) return true;
          })
          .filter((item) => item === true);
        if (itemCheck.length === 1) return true;
      });

      return init.length === 1 && checked.includes(true) ? 0.2 : 0.1;
    }, [total]);

    const pluginRef = useRef({
      id: "afterDatasetsDraw",
      afterDatasetsDraw(chart: Chart) {
        const { ctx } = chart;
        ctx.save();
        data.forEach((item, index) => {
          if (chart.isDatasetVisible(index)) {
            const datasetMeta = chart.getDatasetMeta(index);
            datasetMeta.data.forEach((dataPoint, i) => {
              ctx.font = "600 10px Inter";
              ctx.fillStyle = "#0F132499";
              ctx.textAlign = "center";
              const xPos = dataPoint.x;
              const yPos = dataPoint.y - 5;
              const rate = (item.values[i] / totalSum) * 100;
              if (Math.round(rate) > 1) ctx.fillText(`${rate.toFixed(1)}%`, xPos, yPos);
            });
          }
        });
        ctx.restore();
      },
    });

    useEffect(() => {
      pluginRef.current.afterDatasetsDraw = function (chart: Chart) {
        const { ctx } = chart;
        ctx.save();
        data.forEach((item, index) => {
          if (chart.isDatasetVisible(index)) {
            const datasetMeta = chart.getDatasetMeta(index);
            datasetMeta.data.forEach((dataPoint, i) => {
              ctx.font = "600 10px Inter";
              ctx.fillStyle = "#0F132499";
              ctx.textAlign = "center";
              const xPos = dataPoint.x;
              const yPos = dataPoint.y - 5;
              const rate = (item.values[i] / totalSum) * 100;
              if (Math.round(rate) > 1) ctx.fillText(`${rate.toFixed(1)}%`, xPos, yPos);
            });
          }
        });
        ctx.restore();
      };
    }, [data, totalSum]);

    return (
      <div className="h-[338px] w-full">
        <BarChartRender
          ref={ref}
          className="!h-auto !w-full"
          data={{
            labels: labels,
            datasets: data.map((item) => ({
              label: item.label,
              data: item.values,
              backgroundColor: item.backgroundColor,
              borderRadius: 2,
              borderSkipped: false,
              barThickness: 27,
              barPercentage: 0.5,
              categoryPercentage: 0.8,
              maxBarThickness: 20,
            })),
          }}
          options={{
            indexAxis: "x",
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false,
              },
              tooltip: {
                position: 'nearest',
                enabled: false,
                external(args) {
                  const { chart, tooltip } = args;
                  const tooltipEl = getOrCreateTooltip(chart, "Age");
                  if (tooltip.opacity === 0) {
                    tooltipEl.style.opacity = 0;
                    return;
                  }
                  if (tooltip.body) {
                    const tableRoot = tooltipEl.querySelector(".chartjs-tooltip > div");
                    const titleLines = tooltip.title || [];
                    const bodyLines = tooltip.body.map((b) => b.lines);

                    const tableHead = document.createElement("div");

                    titleLines.forEach(() => {
                      const tr = document.createElement("div");
                      tableHead.appendChild(tr);
                      const tableBody = document.createElement("div");
                      tableBody.className = "table-body-tooltips";
                      bodyLines.forEach((body, i) => {
                        const value = body[0].split(":");
                        const colors = tooltip.labelColors[i];
                        const html = `
                          <div class="min-w-[139px] flex flex-col gap-0.5 py-2 px-[13px] 3xl:gap-1">
                            <div class="value-tooltips">
                                <div class="dots-tooltip"></div>
                                <div class="font-medium text-xs text-secondary 3xl:text-md">${
                                  value[0]
                                }</div>
                            </div>
                            <div class="text-primary font-medium text-xl 3xl:text-3xl">${
                              value[1]
                            }</div>
                            <div class="font-medium text-xs text-secondary 3xl:text-md">${(
                              (removeFormatNumber(value[1]) / totalSum) *
                              100
                            ).toFixed(1)}%</div>
                          </div>`;
                        tableBody.innerHTML = html;
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        // @ts-expect-error
                        tableBody.querySelector(".dots-tooltip").style.backgroundColor =
                          colors.backgroundColor;
                      });

                      // Remove old children
                      while (tableRoot.firstChild) {
                        tableRoot.firstChild.remove();
                      }

                      // Add new children
                      tableRoot.appendChild(tableHead);
                      tableRoot.appendChild(tableBody);
                    });
                    const { offsetLeft: positionX, offsetTop: positionY } = chart.canvas;
                    // Display, position, and set styles for font
                    tooltipEl.style.opacity = "1";
                    tooltipEl.style.left = positionX + tooltip.caretX + "px";
                    tooltipEl.style.top = positionY + tooltip.caretY + "px";
                  }
                },
              },
            },
            scales: {
              x: {
                beginAtZero: true,
                grid: {
                  display: false,
                },
              },
              y: {
                grid: {
                  circular: true,
                },
                ticks: {
                  callback: function (value) {
                    const totalValue = (Number(value) / totalSum) * 100;
                    if (totalValue <= 110) return `${totalValue.toFixed(0)}%`;
                  },
                  stepSize: step * totalSum,
                },
                border: {
                  display: false,
                },
                grace: data.length === 1 ? "10%" : "5%",
              },
            },
          }}
          plugins={[pluginRef.current]}
        />
      </div>
    );
  }
);

export default Barchart;
