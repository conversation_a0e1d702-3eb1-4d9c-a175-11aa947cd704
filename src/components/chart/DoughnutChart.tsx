'use client'
import { Doughn<PERSON> } from "react-chartjs-2";
import { Chart, ArcElement, Tooltip, Legend, Title } from "chart.js";
import { useMemo } from "react";
import { donutChartTooltip, getOrCreateTooltip } from "./utils";
import { cn } from '@/lib/utils';

Chart.register(ArcElement, Tooltip, Legend, Title);

type Props = {
  values: number[];
  backgroundColors?: string[];
  labels: string[];
  className?: string;
  showTotal?: boolean;
  titleTooltip: string;
  showDots?: boolean;
};

const PolarChart: React.FC<Props> = ({
  values,
  backgroundColors,
  labels,
  className,
  showTotal = false,
  titleTooltip,
  showDots = true,
}) => {
  const data = {
    labels: labels,
    datasets: [
      {
        data: values,
        backgroundColor: backgroundColors
          ? backgroundColors
          : [
              "hsla(266, 77%, 61%, 1)",
              "hsla(265, 77%, 72%, 1)",
              "hsla(29, 90%, 57%, 1)",
              "hsla(224, 8%, 65%, 1)",
            ],
      },
    ],
  };

  const total = useMemo(() => values.reduce((a, b) => a + b, 0), [values]);

  const legendItems = data.labels.map((label, index) => ({
    text: label,
    fillStyle: data.datasets[0].backgroundColor[index],
  }));

  return (
    <div className="w-full h-full flex flex-col items-center">
      <div
        className={cn(
          "w-full max-w-[147px] flex flex-col justify-center h-full 3xl:max-w-[255px]",
          className
        )}
      >
        <Doughnut
          key={total}
          className="!h-auto !w-full !m-auto"
          data={data}
          options={{
            elements: {
              arc: {
                borderWidth: 0,
              },
            },
            radius: "100%",
            cutout: "75%",
            responsive: true,
            plugins: {
              legend: {
                display: false,
              },
              tooltip: {
                position: "nearest",
                enabled: false,
                external(args) {
                  const { chart, tooltip } = args;

                  const tooltipEl = getOrCreateTooltip(chart, titleTooltip);

                  if (tooltip.opacity === 0) {
                    tooltipEl.style.opacity = 0;
                    return;
                  }

                  if (tooltip.body) {
                    const tableRoot = tooltipEl.querySelector(".chartjs-tooltip > div");
                    const titleLines = tooltip.title || [];
                    const bodyLines = tooltip.body.map((b) => b.lines);
                    tableRoot.innerHTML = "";
                    titleLines.forEach((title, index) => {
                      const body = bodyLines[index];
                      const value = body[0].split(":");
                      const colors = tooltip.labelColors[index];
                      const tooltipHtml = donutChartTooltip(
                        title,
                        value[0],
                        total,
                        colors.backgroundColor
                      );
                      tableRoot.insertAdjacentHTML("beforeend", tooltipHtml);
                    });
                    const { offsetLeft: positionX, offsetTop: positionY } = chart.canvas;
                    tooltipEl.style.opacity = "1";
                    tooltipEl.style.left = `${positionX + tooltip.caretX}px`;
                    tooltipEl.style.top = `${positionY + tooltip.caretY}px`;
                  }
                },
              },
            },
          }}
          plugins={
            (showTotal && [
              {
                id: "afterDatasetsDraw",
                beforeDatasetsDraw(chart) {
                  const { ctx } = chart;
                  const xCoor = chart.getDatasetMeta(0).data[0].x;
                  const yCoor = chart.getDatasetMeta(0).data[0].y;
                  ctx.font = `600 12px Inter`;
                  ctx.textAlign = "center";
                  ctx.textBaseline = "middle";
                  ctx.fillStyle = "#0F132499";
                  ctx.fillText("Total", xCoor, yCoor - 10);
                  ctx.restore();
                  ctx.font = `600 24px Inter`;
                  ctx.textAlign = "center";
                  ctx.textBaseline = "middle";
                  ctx.fillStyle = "#14151A";
                  ctx.fillText(Number(total).toLocaleString(), xCoor, yCoor + 10);
                  ctx.restore();
                },
              },
            ]) ||
            []
          }
        />
      </div>
      {showDots && (
        <ul className="flex w-full items-center mt-4 text-xs flex-wrap justify-center gap-2">
          {legendItems.map((item, index) => (
            <li key={index} className="flex flex-shrink-0 items-center gap-1">
              <button
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: item.fillStyle }}
              ></button>
              <span className="flex-shrink-0">{item.text}</span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default PolarChart;
