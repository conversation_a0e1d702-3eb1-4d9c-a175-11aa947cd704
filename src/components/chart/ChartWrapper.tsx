'use client';
import { RiLoader2Line } from '@remixicon/react';
import EmptyData from './components/EmptyDta';
import { cn } from '@/lib/utils';
import SelectOptions from '@/components/SelectOptions';
import React from 'react';

type Props = {
  children: React.ReactNode;
  title: string;
  className?: string;
  options?: { label: string; value: string }[];
  onChange?: (value: string) => void;
  loading?: boolean;
  contentRight?: React.ReactNode;
  isEmptyData?: boolean;
};

const ChartWrapper: React.FC<Props> = ({
  children,
  loading,
  title,
  className,
  options,
  onChange,
  contentRight,
  isEmptyData,
}) => {
  return (
    <div
      className={cn(
        "rounded-2xl p-6 pt-[22px] min-h-[275px] flex flex-col items-center gap-6 shadow-chart",
        className
      )}
    >
      <div className="font-semibold h-6 text-base text-start flex gap-2 justify-start items-center w-full">
        {options && onChange ? (
          <SelectOptions
            className="w-fit"
            placeholder={options[0].label}
            options={options}
            onChange={onChange}
          />
        ) : (
          <>{title}</>
        )}
        {contentRight}
      </div>
      {loading ? (
        <div className="flex-1">
          <RiLoader2Line className="animate-spin" size={30} />
        </div>
      ) : isEmptyData ? (
        <EmptyData />
      ) : (
        children
      )}
    </div>
  );
};

export default ChartWrapper;
