import { formatTitle, getOriginPoints, getSuitableY } from '@/utils/chart';
import { capitalizeOfEachWord, removeFormatNumber } from '@/utils/utils';

export const getOrCreateTooltip = (chart: any, title: string) => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  let tooltipEl = chart.canvas.parentNode.querySelector<HTMLDivElement>(".chartjs-tooltip");

  if (!tooltipEl) {
    tooltipEl = document.createElement("div");
    tooltipEl.className = "chartjs-tooltip";
    tooltipEl.style.opacity = "0";
    tooltipEl.style.position = "absolute";
    tooltipEl.style.pointerEvents = "none";

    const header = document.createElement("header");
    header.className = "chartjs-tooltip-header";
    tooltipEl.appendChild(header);

    const table = document.createElement("div");
    table.className = "chartjs-tooltip-body";
    tooltipEl.appendChild(table);

    chart.canvas.parentNode.appendChild(tooltipEl);
  }

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  const header = tooltipEl.querySelector<HTMLDivElement>(".chartjs-tooltip-header");
  if (header) {
    header.innerText = title;
  }

  return tooltipEl;
};

export const polarChartPlugins = (values: number[], total: number) => [
  {
    id: "drawThickness",
    beforeDraw(chart: any) {
      const baseRadius = 100;
      const data = chart.data.datasets[0].data as number[];

      const sortedIndices = data
        .map((value, index) => ({ value, index }))
        .sort((a, b) => b.value - a.value);

      sortedIndices.forEach(({ index }, rank) => {
        const segment = chart.getDatasetMeta(0).data[index];
        const radius = baseRadius - rank * 15;

        segment.outerRadius = radius > 0 ? radius : 10;
      });
    },
  },
  {
    id: "shadowPlugin",
    beforeDraw: (chart: any) => {
      const ctx = chart.ctx;
      ctx.save();
      chart.data.datasets.forEach((_: number, i: number) => {
        const meta = chart.getDatasetMeta(i);
        meta.data.forEach((element: any) => {
          ctx.shadowColor = "rgba(0, 0, 0, 0.5)";
          ctx.shadowBlur = 10;
          ctx.shadowOffsetX = 4;
          ctx.shadowOffsetY = 4;
          (element as any).draw(ctx);
          ctx.shadowColor = "transparent";
          ctx.shadowBlur = 0;
          ctx.shadowOffsetX = 0;
          ctx.shadowOffsetY = 0;
        });
      });
      ctx.restore();
    },
  },
  {
    id: "arrowPlugin",
    afterDatasetsDraw: (chart: any) => {
      const ctx = chart.ctx;
      ctx.save();
      ctx.font = "10px ' Inter'";
      const leftLabelCoordinates: number[] = [];
      const rightLabelCoordinates: number[] = [];
      const chartCenterPoint = {
        x: (chart.chartArea.right - chart.chartArea.left) / 2 + chart.chartArea.left,
        y: (chart.chartArea.bottom - chart.chartArea.top) / 2 + chart.chartArea.top,
      };
      chart.config.data.labels?.forEach((label: any, i: number) => {
        const percentage = (values[i] / total) * 100;
        if (Number(percentage.toFixed(1)) === 0) return;
        const meta = chart.getDatasetMeta(0);
        const arc = meta.data[i];
        const centerPoint = arc.getCenterPoint();
        const color = chart.config._config.data.datasets[0].backgroundColor[i];

        const angle = Math.atan2(
          centerPoint.y - chartCenterPoint.y,
          centerPoint.x - chartCenterPoint.x
        );
        const outerRadius = arc.outerRadius;
        const originPoint = getOriginPoints(chartCenterPoint, centerPoint, outerRadius);
        const point2X =
          chartCenterPoint.x +
          Math.cos(angle) *
            (centerPoint.x < chartCenterPoint.x ? outerRadius + 10 : outerRadius + 10);
        let point2Y =
          chartCenterPoint.y +
          Math.sin(angle) *
            (centerPoint.y < chartCenterPoint.y ? outerRadius + 15 : outerRadius + 15);

        let suitableY;

        if (point2X < chartCenterPoint.x) {
          suitableY = getSuitableY(point2Y, leftLabelCoordinates, "left");
        } else {
          suitableY = getSuitableY(point2Y, rightLabelCoordinates, "right");
        }

        point2Y = suitableY;

        const edgePointX =
          point2X < chartCenterPoint.x
            ? chartCenterPoint.x - outerRadius - 10
            : chartCenterPoint.x + outerRadius + 10;

        if (point2X < chartCenterPoint.x) {
          leftLabelCoordinates.push(point2Y);
        } else {
          rightLabelCoordinates.push(point2Y);
        }
        const labelAlignStyle = edgePointX < chartCenterPoint.x ? "right" : "left";
        const labelX = edgePointX < chartCenterPoint.x ? edgePointX : edgePointX + 0;
        const labelY = point2Y + 3;
        if (percentage < 10) {
          const meta = chart.getDatasetMeta(0);
          const arc = meta.data[0];
          const test = i == 3 ? i * 6 : i % 2 === 0 ? i : i * 5;
          const radius = (arc.outerRadius + arc.innerRadius) / 2 - test;

          ctx.lineWidth = 2;
          ctx.strokeStyle = color;
          ctx.beginPath();
          ctx.moveTo(originPoint.x, originPoint.y);
          ctx.lineTo(point2X, radius);
          ctx.stroke();

          ctx.beginPath();
          ctx.moveTo(point2X, radius);
          ctx.lineTo(edgePointX - test, radius - test);
          ctx.stroke();

          ctx.textAlign = labelAlignStyle;
          ctx.textBaseline = "bottom";
          ctx.font = "500 10px Inter";
          ctx.fillStyle = "black";
          ctx.fillText(
            formatTitle("" + label),
            i === 3 ? labelX - test - 10 : labelX - test,
            i === 3 ? radius - 20 : radius - 7
          );
          ctx.textBaseline = "bottom";
          ctx.font = "600 10px Inter";
          ctx.fillStyle = color;
          ctx.fillText(
            ((values[i] / total) * 100).toFixed(1) + "%",
            i === 3 ? labelX - test - 10 : labelX - test,
            i === 3 ? radius - 7 : radius + 7
          );
          return;
        }
        ctx.lineWidth = 2;
        ctx.strokeStyle = color;
        ctx.beginPath();
        ctx.moveTo(originPoint.x, originPoint.y);
        ctx.lineTo(point2X, point2Y);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(point2X, point2Y);
        ctx.lineTo(edgePointX, point2Y);
        ctx.stroke();

        ctx.textAlign = labelAlignStyle;
        ctx.textBaseline = "bottom";
        ctx.font = "500 10px Inter";
        ctx.fillStyle = "black";
        ctx.fillText(formatTitle("" + label), labelX, labelY - 7);
        ctx.textBaseline = "bottom";
        ctx.font = "600 10px Inter";
        ctx.fillStyle = color;
        ctx.fillText(((values[i] / total) * 100).toFixed(1) + "%", labelX, labelY + 7);
      });
      ctx.restore();
    },
  },
  {
    id: "afterDatasetsDraw",
    afterDatasetsDraw(chart: any) {
      const { ctx } = chart;
      const xCoor = chart.getDatasetMeta(0).data[0].x;
      const yCoor = chart.getDatasetMeta(0).data[0].y;
      ctx.save();
      ctx.beginPath();
      const radius = 35;
      ctx.arc(xCoor, yCoor, radius, 0, 2 * Math.PI);
      ctx.fillStyle = "white";
      ctx.fill();
      ctx.font = `500 10px Inter pont`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillStyle = "#14151A";
      ctx.fillText("Total", xCoor, yCoor - 7);
      ctx.restore();
      ctx.font = `600 12px Inter`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillStyle = "#14151A";
      ctx.fillText(Number(total).toLocaleString(), xCoor, yCoor + 7);
    },
  },
];

export const singleTooltip = (total: number, color: any, title: string, valueReverted: number) => {
  return `
  <div class="min-w-[139px] flex flex-col gap-0.5 py-2 px-[13px] 3xl:gap-1 z-[9999]">
    <div class="value-tooltips">
      <div class="dots-tooltip" style="background-color: ${color};"></div>
      <div class="font-medium text-xs text-secondary 3xl:text-md">${title}</div>
    </div>
    <div class="text-primary font-medium text-xl 3xl:text-3xl">${valueReverted.toLocaleString()}</div>
   <div class="font-medium text-xs text-secondary 3xl:text-md">${(
     (valueReverted / total) *
     100
   ).toFixed(1)}%</div>
  </div>
`;
};
export const barChartHorizontalPlugins = (total: number, labels: string[], values: number[]) => [
  {
    id: "afterDatasetsDraw",
    afterDatasetsDraw(chart: any) {
      const {
        ctx,
        scales: { y },
        chartArea: { left },
      } = chart;
      ctx.save();
      const datasetMeta = chart.getDatasetMeta(0);

      datasetMeta.data.forEach((dataPoint: any, index: number) => {
        ctx.font = "medium 14px Inter";
        ctx.fillStyle = "#14151A";
        ctx.textAlign = "left";
        ctx.textBaseline = "middle";
        ctx.fillText(capitalizeOfEachWord(labels[index]), left, y.getPixelForValue(index) - 18);

        ctx.font = "medium 14px Inter";
        ctx.fillStyle = "#0F132499";
        ctx.textAlign = "center";
        const xPos = dataPoint.x + 30;
        const yPos = y.getPixelForValue(index) + 5;
        const rate = (values[index] / total) * 100;
        ctx.fillText(`${rate.toFixed(1)}%`, xPos, yPos - 5);
      });
      ctx.restore();
    },
  },
];

export const barChartTooltip = (totalValue: number, title: string[], value: string) => {
  return `
    <div class="min-w-[139px] flex flex-col gap-0.5 py-2 px-[13px] 3xl:gap-1">
    <div class="value-tooltips">
        <div class="dots-tooltip hidden"></div>
        <div class="font-medium text-xs text-secondary 3xl:text-md">${title}</div>
    </div>
    <div class="text-primary font-medium text-xl 3xl:text-3xl">${value}</div>
    <div class="font-medium text-xs text-secondary 3xl:text-md">${(
      (removeFormatNumber(value) / totalValue) *
      100
    ).toFixed(1)}%</div>
  </div>`;
};
export const donutChartTooltip = (title: string, value: string, total: number, color: any) => {
  return `
    <div class="min-w-[139px] flex flex-col gap-0.5 py-2 px-[13px] 3xl:gap-1 z-[9999]">
      <div class="value-tooltips">
        <div
          class="dots-tooltip"
          style="background-color: ${color};"
        ></div>
        <div class="font-medium text-xs text-secondary 3xl:text-md">${title}</div>
      </div>
      <div class="text-primary font-medium text-xl 3xl:text-3xl">${value[0]}</div>
      <div class="font-medium text-xs text-secondary 3xl:text-md">
        ${((removeFormatNumber(value) / total) * 100).toFixed(1)}%
      </div>
    </div>`;
};
