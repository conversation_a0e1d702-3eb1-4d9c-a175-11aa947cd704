import { Box } from '@/components/Box';
import Link from 'next/link';

export const AboutUsLanding = () => {
  return (
    <Box
      variant={'col-center'}
      className="max-w-[1156px] gap-0 m-auto bg-[#F0F0F0] border-[#8F5CFF] border-t-[4px] rounded-[16px] py-[40px] px-[16px] lg:px-[40px]"
    >
      <div className="flex-1 text-center">
        <h2 className="text-[36px] lg:text-[48px] font-semibold leading-14 text-primary mb-[16px]">
          Who we are
        </h2>
        <p className="w-full mb-[72px]">
          The website <Link prefetch={true} href={'/'}><strong>www.big360.ai</strong></Link> is owned and operated by <strong>Big360 Inc.</strong> As a state-of-the-art data integration platform, <strong>Big360.ai</strong> is designed to empower sellers, recruiters, and marketers with the power of big data. Our mission is to revolutionize data-driven decision-making by offering an intuitive, intelligent solution that enhances search capabilities, streamlines operations, and delivers actionable insights with precision and efficiency.
        </p>
      </div>
      <iframe
        src="https://www.youtube.com/embed/AkREWOUGx9k"
        title="Big360 Introduction"
        className="flex-2 rounded-2xl w-full max-w-full aspect-video"
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        referrerPolicy="strict-origin-when-cross-origin"
        allowFullScreen
      />
    </Box>
  );
};
