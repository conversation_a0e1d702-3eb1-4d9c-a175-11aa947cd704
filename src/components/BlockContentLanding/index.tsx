import { Box } from '../Box';
import React from 'react';
import { cn } from '@/lib/utils';

type Props = {
  title?: React.ReactNode;
  description: React.ReactNode;
  classContent?: string;
  className?: string;
  classTitle?: string;
}
export const BlockContentLanding = ({ title, description, classContent, className, classTitle }: Props) => {
  return (
    <Box variant={'col-center'} className={cn("p-6 bg-[#F0F0F0] rounded-2xl justify-center items-center", className)}>
      {title && <h2 className={cn('text-4xl font-semibold',classTitle)}>
        {title}
      </h2>}
      <div className={cn('text-secondary text-base font-normal leading-6', classContent)}>
        {description}
      </div>
    </Box>
  );
};
