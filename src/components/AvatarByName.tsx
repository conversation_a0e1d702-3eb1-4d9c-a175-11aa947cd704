import { cn } from '@/lib/utils';
import { getLastCharName } from '@/utils/utils';
import { TYPE_SOCIAL } from '@/utils/constant';

type Props = {
  name: string;
  position?: "first" | "last";
  className?: string;
  urlImage?: string;
  type?: TYPE_SOCIAL;
};

const AvatarByName = ({ name, position = "last", className, urlImage, type }: Props) => {
  let styleAvatar = "";
  switch (type) {
    case TYPE_SOCIAL.post:
    case TYPE_SOCIAL.group:
      styleAvatar = "rounded-2xl";
      break;
    default:
      styleAvatar = "rounded-full";
      break;
  }
  return urlImage ? (
    <img
      className={cn("w-8 h-8 rounded-full object-cover", styleAvatar, className)}
      src={urlImage}
      alt="image"
    />
  ) : (
    <div
      className={cn(
        "w-8 h-8 rounded-full flex-shrink-0 bg-[#717684] text-white font-semibold flex items-center justify-center text-xs uppercase",
        styleAvatar,
        className
      )}
    >
      {name ? getLastCharName(name, position) : ""}
    </div>
  );
};

export default AvatarByName;
