import React from 'react';
import FooterLandingPage from '../FooterLandingPage';
import { Header } from '@/components/Header';
import IconFavicon from '@/assets/icon/IconFavicon';
import LogoSvg from '@/assets/icon/LogoSvg';
import { cn } from '@/lib/utils';

type TLandingPageWrapper = {
  children: React.ReactNode;
  isHomePage?: boolean;
  isHideLogo?: boolean;
  isDataset?: boolean;
  className?: string;
  mainClassName?: string;
  containerClassName?: string;
}
export const LandingPageWrapper: React.FC<TLandingPageWrapper> = ({ ...props }: TLandingPageWrapper) => {
  const { children, isHomePage = false, isHideLogo = false, isDataset = false, className, mainClassName, containerClassName } = props;
  return (
    <div className={cn('w-full h-full', className)}>
      <Header />
      <main className={cn('p-4 lg:p-0', mainClassName)}>
        {( !isHomePage && !isHideLogo ) &&
          <div className="flex items-center justify-center gap-2 mt-[40px] max-md:mb-[12px]">
            <IconFavicon width={48} height={48} />
            <LogoSvg width={130} height={48} />
          </div>
        }
        {isDataset && <div>
          <h1 className="text-primary text-center max-md:text-[36px] text-[48px] font-semibold mt-4 md:mt-[80px]">
            Dataset Library
          </h1>
        </div>}
        <div className={cn('mx-auto lg:mt-[40px]', isHomePage ? 'w-full' : 'max-w-[1156px]', containerClassName)}>
          {children}
        </div>
      </main>
      <FooterLandingPage />
    </div>
  );
};
