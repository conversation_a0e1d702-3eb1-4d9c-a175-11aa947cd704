import { Box } from '@/components/Box';
import Image from 'next/image';

export const Features = () => {
  return (
    <div className="max-w-[1156] mx-auto">
      {/*audience dataset*/}
      <Box className="gap-[12px] lg:gap-[72px] mb-12 lg:mb-[48px] flex-col lg:flex-row">
        <div
          className="flex-1 rounded-[16px] p-[24px] pr-0 flex items-center justify-center min-h-[245px] h-full lg:h-[386px] w-full max-w-[542px]"
          style={{
            background: 'radial-gradient(274.75% 182.21% at -25.62% -31.99%, #FDEC8A 11%, rgba(255, 252, 235, 0.50) 86%), #FDFDFD'
          }}
        >
          <Image
            quality={100}
            src={'/features/find-dataset.png'}
            alt={'find-dataset'}
            width={0}
            height={0}
            className="max-[450px]:w-[476px] w-[508px] max-[450px]:h-[166px] h-[261px]"
            sizes="100vw"
          />
        </div>
        <div className="flex-1 lg:p-[24px]">
          <h3 className="text-primary text-[24px] md:text-[36px] font-semibold">
            Find the <span className="text-[#FBCA24]">Right Audience & Dataset</span>
          </h3>
          <p className="text-[#515667] text-[14px] md:text-[18px]">
            Leverage the power of social data and work personas to identify high-quality customer datasets with over 90% accuracy, ensuring precise and effective targeting.
          </p>
        </div>
      </Box>
      {/*segment*/}
      <Box className="gap-4 lg:gap-[72px] mb-8 lg:mb-[48px] flex-col-reverse lg:flex-row">
        <div className="flex-1 lg:p-[24px]">
          <h3 className="text-primary text-[24px] md:text-[36px] font-semibold">
            Data <span className="text-[#F48E2F]">Segment</span>
          </h3>
          <p className="text-[#515667] text-[14px] md:text-[18px]">
            You can create targeted customer segments for multi-channel marketing campaigns based on various classification criteria such as demographics (age/gender), geography (city or map radius), behavior (interests), job position, seniority level, and more. </p>
        </div>
        <div
          className="flex-1 rounded-[16px] p-[24px] flex items-center justify-center min-h-[245px] h-full lg:h-[386px] w-full max-w-[542px]"
          style={{
            background: 'radial-gradient(259.59% 164.34% at 4.92% 10.62%, rgba(254, 241, 213, 0.30) 14%, #FAC881 89%)'
          }}
        >
          <Image
            quality={100}
            src={'/features/data-segment.png'}
            alt={'data-segment'}
            width={0}
            height={0}
            className="max-[450px]:w-[279px] w-[452px] max-[450px]:h-[190px] h-[307px]"
            sizes="100vw"
          />
        </div>
      </Box>
      {/*processing*/}
      <Box className="gap-[12px] lg:gap-[72px] mb-12 lg:mb-[48px] flex-col lg:flex-row">
        <div
          className="flex-1 rounded-[16px] p-[24px] flex items-center justify-center min-h-[245px] h-full lg:h-[386px] w-full max-w-[542px]"
          style={{
            background: 'radial-gradient(254.39% 158.58% at 4.92% 10.62%, rgba(224, 248, 227, 0.10) 14%, #5CCC70 89%), #FDFDFD'
          }}
        >
          <Image quality={100} src={'/features/data-processing.png'} alt={'data-processing'} width={508} height={261} />
        </div>
        <div className="flex-1 lg:p-[24px]">
          <h3 className="text-primary text-[24px] md:text-[36px] font-semibold">
            Data <span className="text-[#27923A]">Processing</span>
          </h3>
          <p className="text-[#515667] text-[14px] md:text-[18px]">
            The system enables you to process user data files in various ways (Mix, Intersect, Minus), allowing you to create larger datasets or more precisely targeted audiences.
          </p>
        </div>
      </Box>
      {/* Enrichment */}
      <Box className="gap-4 lg:gap-[72px] mb-8 lg:mb-[48px] flex-col-reverse lg:flex-row">
        <div className="flex-1 lg:p-[24px]">
          <h3 className="text-primary text-[24px] md:text-[36px] font-semibold">
            Data <span className="text-[#2C9EFF]">Enrichment</span>
          </h3>
          <p className="text-[#515667] text-[14px] md:text-[18px]">
            Turn any phone number into a complete contact profile. Instantly enhance customer insights from your CRM or CSV file within seconds using Big360&#39;s powerful big data.
          </p>
        </div>
        <div
          className="flex-1 rounded-[16px] p-[24px] flex items-center justify-center min-h-[245px] lg:h-[386px] w-full max-w-[542px]"
          style={{
            background: 'radial-gradient(275.48% 172.64% at 126.56% 117.43%, #8ED7FF 11%, rgba(238, 249, 255, 0.50) 85%)'
          }}
        >
          <Image quality={100} src={'/features/data-enrichment.png'} alt={'data-enrichment'} width={452} height={263} />
        </div>
      </Box>
      {/* Engage */}
      <Box className="gap-[12px] lg:gap-[72px] mb-12 lg:mb-[48px] flex-col lg:flex-row">
        <div
          className="flex-1 rounded-[16px] p-[24px] flex items-center justify-center min-h-[245px] lg:h-[386px] w-full max-w-[542px]"
          style={{
            background: 'radial-gradient(255.35% 164.91% at -17.07% -16.15%, #A585FF 11%, rgba(226, 218, 255, 0.10) 86%), #FDFDFD'
          }}
        >
          <Image quality={100} src={'/features/engage-customer.png'} alt={'engage-customer'} width={452} height={263} />
        </div>
        <div className="flex-1 lg:p-[24px]">
          <h3 className="text-primary text-[24px] md:text-[36px] font-semibold">
            Seamless{' '}<span className="text-[#8F5CFF]">integration</span>
          </h3>
          <p className="text-[#515667] text-[14px] md:text-[18px]">
            Connect your sales and marketing teams with customers across all channels (Call center, Email, Facebook ads, Tiktok Ads, Google Ads, Zalo ZNS and more) on a single unified platform - CRM360          </p>
        </div>
      </Box>
    </div>
  );
};
