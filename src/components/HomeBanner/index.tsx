import IconFavicon from '@/assets/icon/IconFavicon';
import LogoSvg from '@/assets/icon/LogoSvg';
import React from 'react';

export const HomeBanner = () => {
  return (
    <div
      className="pt-[40px] pb-[40px] py-[142px]"
      style={{
        background: 'radial-gradient(197.48% 152.51% at 2.6% 12.86%, #E2DAFF 14%, #FFF 89%)'
      }}
    >
      <div className="flex items-center justify-center gap-2 mt-[40px]">
        <IconFavicon width={48} height={48} />
        <LogoSvg width={130} height={48} />
      </div>
      <div className="text-primary text-center">
        <h1 className="text-[48px] font-semibold leading-14 text-center mt-8 mx-auto">
          The Ultimate Tool for<br />
          Finding Potential Customers
        </h1>
        <h3 className="text-[18px] font-medium mt-[24px]">Leverage Data – Expand Opportunities – Achieve Sustainable Growth</h3>
        <img src={'/imageBannerHome.png'} alt={'imageBannerHome'} width={1156} height={650} className="mx-auto rounded-2xl mt-[40px]" />
      </div>

    </div>
  );
};
