import { RiMailLine, RiMapPinLine, Ri<PERSON><PERSON>engerFill, RiPhoneLine } from '@remixicon/react';
import Link from 'next/link';
import React from 'react';
import { Box } from '@/components/Box';
// import { Button } from '@/components/ui/button';
// import { Input } from '@/components/ui/input';
import IconFavicon from '@/assets/icon/IconFavicon';
import LogoSvg from '@/assets/icon/LogoSvg';
import { SUPPORT_MESSENGER } from '@/utils/constant';


const FooterTop = () => {

  return (
    <Box className="w-full justify-between flex-col lg:flex-row items-start mb-2 pt-4 px-[16px] gap-4">
      <Box variant="col-start" className="gap-2 flex-1">
        <Link prefetch={true} href={'/'} className="relative items-center gap-2 flex flex-row md:flex">
          <IconFavicon />
          <LogoSvg />
        </Link>
        <div className="text-primary text-sm font-semibold">Big360 Inc.</div>
        <p className='text-secondary font-medium text-sm'>
          EIN Number: 98-1841781
        </p>
        <Box className="gap-2 justify-start text-secondary font-medium text-sm">
          <RiMapPinLine size={16} />
          <span>1001 S. Main St. STE 700, Kalispell, MT 59901</span>
        </Box>
        <a href="tel:+14063087751">
          <Box className="gap-2 justify-start text-secondary font-medium text-sm">
            <RiPhoneLine size={16} />
            <span>+14063087751</span>
          </Box>
        </a>
        <a href="mailto:<EMAIL>">
          <Box className="gap-2 justify-start text-secondary font-medium text-sm">
            <RiMailLine size={16} />
            <EMAIL>
          </Box>
        </a>
      </Box>
      <Box variant={'col-start'} className='gap-4 max-[1200px]:w-[270px] max-[1200px]:max-w-[270px] xl:max-w-full lg:flex-1 lg:gap-3'>
        <Box className="items-start flex-col lg:flex-row max-[1200px]:w-full min-[1200px]:w-4/5 gap-4">
          <Box variant="col-start" className="gap-2 w-fit">
            <div className="text-primary text-sm font-semibold">Our Company</div>
            <Link prefetch={true} href={'/about-us'} className="text-secondary text-sm font-normal">About Us</Link>
            <Link prefetch={true} href={'/contact'} className="text-secondary text-sm font-normal">Contact</Link>
            <Link prefetch={true} href={'/term-of-service'} className="text-secondary text-sm font-normal">Terms of Service</Link>
          </Box>
          <Box variant="col-start" className="gap-2 w-fit">
            <div className="text-primary text-sm font-semibold">Policy</div>
            <Link prefetch={true} href={'/refund-policy'} className="text-secondary text-sm font-normal">Refund Policy</Link>
            <Link prefetch={true} href={'/cancellation-policy'} className="text-secondary text-sm font-normal">Cancellation Policy</Link>
            <Link prefetch={true} href={'/privacy-policy'} className="text-secondary text-sm font-normal">Privacy Policy</Link>
          </Box>
        </Box>
        <Box className="flex-col items-start gap-2">
          <div className="text-primary text-sm font-semibold">Join our community</div>
          <SocialLink
            url={SUPPORT_MESSENGER}
            icon={<RiMessengerFill size={16} />}
            text="Messenger Support"
          />
        </Box>
        {/*<Box variant={'col-start'} className='gap-2'>*/}
        {/*  <div className="text-primary text-sm font-medium">Join our community</div>*/}
        {/*  <div className="flex w-full max-w-sm items-center gap-[6px]">*/}
        {/*    <Input*/}
        {/*      type="email"*/}
        {/*      placeholder="Enter your email"*/}
        {/*      className="border-custom-primary rounded-xl h-10 focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0"*/}
        {/*    />*/}
        {/*    <Button type="submit" variant="main" className="m-0 rounded-xl text-sm h-10 w-[84px]">*/}
        {/*      Subscribe*/}
        {/*    </Button>*/}
        {/*  </div>*/}
        {/*</Box>*/}
      </Box>
    </Box>
  );
};

export default FooterTop;

const SocialLink = ({ url, icon, text }: { url: string; icon:React.ReactNode; text: string }) => (
  <Link href={url} target="_blank">
    <Box className="gap-2 justify-start text-primary font-medium text-sm">
      {icon}
      <span>{text}</span>
    </Box>
  </Link>
);
