"use client";
import Typewriter from "typewriter-effect";

export const EffectTyping = () => {
  return (
    <div className="lg:inline-block w-fit whitespace-nowrap">
      <Typewriter
        options={{
          strings: [
            "Finding Audience",
            "Data Segmentation",
            "Data Processing",
            "Data Enrichment",
            "Seamless integration",
          ],
          autoStart: true,
          loop: true,
          wrapperClassName: "text-[#A585FF]",
        }}
      />
    </div>
  );
};
