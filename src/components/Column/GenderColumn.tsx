import { cn } from '@/lib/utils';

const GenderColumn = ({ value }: { value: any }) => {
  const getClassName = () => {
    switch (value) {
      case "F":
        return "bg-[#FCE5E4] text-[#9A1C13]";
      case "M":
        return "bg-[#E3EAFD] text-[#133A9A]";
      case null:
        return "";
      case "":
        return "";
      default:
        break;
    }
  };
  const formatInput = () => {
    switch (value) {
      case "F":
        return "Female";
      case "M":
        return "Male";
      case null:
        return "-";
      case "":
        return "-";
      default:
        return value;
    }
  };
  return (
    <div className={cn("py-0.5 text-center px-1.5 rounded-sm", getClassName())}>
      {formatInput()?.toLowerCase()}
    </div>
  );
};

export default GenderColumn;
