import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const DefaultColumn = ({ value, isLowercase, className }: any) => {
  return (
    <TooltipProvider delayDuration={300}>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className={cn("py-2 overflow-hidden text-ellipsis w-fit whitespace-nowrap md:max-w-[100px]  lg:max-w-[150px] xl:max-w-[350px] inline-block", className)}>
            {String(value) === "null" || "" ? "-" : isLowercase ? String(value).toLowerCase() : String(value)}
          </span>
        </TooltipTrigger>
        <TooltipContent side="top" sideOffset={-40} className="z-30">
          <span className="py-2 ">
            {String(value) === "null" || "" ? "-" : isLowercase ? String(value).toLowerCase() : String(value)}
          </span>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
export default DefaultColumn;
