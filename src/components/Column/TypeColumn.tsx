import { RiAdvertisementLine, Ri<PERSON>arth<PERSON>ine, Ri<PERSON>ive<PERSON>ine, RiLock2Line } from "@remixicon/react";


import { Box } from '@/components/Box';
import { cn } from '@/lib/utils';
import { SUB_TYPE_SOCIAL } from '@/utils/SocialData';
import { audienceType, SUB_TYPE_SOCIAL_LABEL } from '@/utils/constant';

export const SubTag = ({
  icon,
  text,
  className,
}: {
  icon: React.ReactNode
  text: string;
  className?: string;
}) => (
  <Box className={cn("gap-1 text-[12px] font-semibold p-1 bg-secondary rounded-sm", className)}>
    <span> {icon}</span>
    <span className="capitalize">
      {text}
    </span>
  </Box>
);
const TypeColumn = (params: any) => {
  const type = params?.audience?.type;
  const subtype = params?.audience?.subtype;
  return (
    <div className="text-center">
      {(type || params?.audience) && (
        <Box variant="col-start" className="text-secondary gap-0 items-center py-2">
          <div className="uppercase text-xs lg:text-sm font-semibold">
            {audienceType(type)?.name}
          </div>
          {subtype && (
            <>
              {subtype == SUB_TYPE_SOCIAL.ad_post && (
                <SubTag
                  icon={<RiAdvertisementLine size={16} />}
                  text={'Sponsor'}
                />
              )}
              {subtype == SUB_TYPE_SOCIAL.live_post && (
                <SubTag icon={<RiLiveLine size={16} />} text={'Live'} />
              )}
              {subtype == SUB_TYPE_SOCIAL.private_group && (
                <SubTag
                  icon={<RiLock2Line size={16} />}
                  text={SUB_TYPE_SOCIAL_LABEL[subtype - 1].name}
                />
              )}
              {subtype == SUB_TYPE_SOCIAL.public_group && (
                <SubTag
                  icon={<RiEarthLine size={16} />}
                  text={SUB_TYPE_SOCIAL_LABEL[subtype - 1].name}
                />
              )}
            </>
          )}
        </Box>
      )}
      {(!type || params?.audience == null) && "--"}
    </div>
  );
};

export default TypeColumn;
