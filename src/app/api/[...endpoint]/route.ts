import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON>ogger } from '@/lib/withLogger';

const handler = async (request: NextRequest) => {
  const urlObj = new URL(request.url);
  const searchParams = urlObj.searchParams;

  const token = process.env.NEXT_PUBLIC_TOKEN ?? '';
  const baseUrl = process.env.NEXT_PUBLIC_API_URL + '/api/v1/dataset-library/social-audience';

  // 👇 Lấy endpoint từ URL thủ công (vì không có context.params trong handler)
  const pathname = urlObj.pathname; // e.g. /api/something/here
  const endpoint = pathname.replace(/^\/api\//, ''); // lấy phần sau `/api/`

  const url = new URL(`${baseUrl}/${endpoint}`);
  searchParams.forEach((value, key) => {
    url.searchParams.append(key, value);
  });

  try {
    const response = await fetch(url.toString(), {
      headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': '69420',
        'x-api-key': token
      }
    });

    if (!response.ok) {
      return NextResponse.json({ error: 'Error fetching data' }, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const GET = withLogger(handler);
