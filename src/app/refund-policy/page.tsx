import { BlockContentLanding } from '@/components/BlockContentLanding';
import { LandingPageWrapper } from '@/components/LandingPageWrapper';
import { RuleContent } from '@/components/RuleContent';
import React from 'react';
import { HeaderPage } from '@/components/HeaderPage';

export async function generateMetadata() {
  return {
    title: 'Refund Policy - The Best Social Data Platform in Vietnam',
  };
}

export default function RefundPolicyPage() {
  return (
    <LandingPageWrapper>
      <div className="text-center max-w-[1156px] mx-auto">
        <HeaderPage
          title={<>Refund Policy</>}
        />
        <p className="mb-[40px]">
          <strong>Last Updated:</strong> March 3, 2025
        </p>
        <BlockContentLanding
          description={<>
            <p className="text-primary text-base mb-[24px]">
              This refund policy applies to all transactions made at <strong>Big360 Inc.</strong> By using our products/services, customers agree to the terms outlined in this policy.
            </p>
          </>}
        />
      </div>
      <div className="max-w-[1156px] mx-auto mt-[40px]">
        <RuleContent
          title={'1. Information We Collect'}
          description={
            <>
              We only accept refunds in the following cases:
              <br />
              <br />
              <ul className="list-disc pl-6">
                <li>
                  The products/services have a critical error affecting its functionality that we cannot resolve within a reasonable time.
                </li>
                <li>
                  The products/services have made a payment but have not received an activation code or access to the software.
                </li>
                <li>
                  The products/services does not function as described or is incompatible with the customer’s system (provided installation and usage instructions were followed correctly).
                </li>
                <li>
                  The refund request is submitted within the specified period from the date of purchase.
                </li>
              </ul>
            </>
          }
        />
        <RuleContent
          title={'2. Refund Request Process'}
          description={
            <>
              Customers can request a refund by:
              <br />
              <br />
              <ul className="list-decimal pl-6">
                <li>
                  Contacting our customer support team via{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="underline text-[#8F5CFF]"
                  >
                    <EMAIL>
                  </a>
                </li>
                <li>
                  Providing order details, the reason for the refund request, and evidence (screenshots of errors, issue descriptions, etc.).
                </li>
                <li>
                  Waiting for confirmation from our customer service team.
                </li>
              </ul>
            </>
          }
        />
        <RuleContent
          title={'3. Refund Processing Time'}
          description={
            <>

              <ul className="list-disc pl-6">
                <li>
                  Refund requests will be reviewed and responded to within 15 business days.
                </li>
                <li>
                  If approved, the refund will be processed to the original payment method within 5 business days.
                </li>
              </ul>
            </>
          }
        />
        <RuleContent
          title={'4. Cases Not Eligible for Refund'}
          description={
            <>

              <ul className="list-disc pl-6">
                <li>
                  The customer changes their mind after purchasing the product/service.
                </li>
                <li>
                  The product/service has been activated or used (unless there is a critical, unresolvable error).
                </li>
                <li>
                  The refund request is submitted after the specified period.
                </li>
                <li>
                  The product/service does not function due to issues with the customer’s system or device.
                </li>
                <li>
                  The product/service purchased as part of a special promotion or discount program (unless refund conditions apply).
                </li>
              </ul>
            </>
          }
        />
        <div className="mb-[40px]">
          <RuleContent
            title={'5. Policy Changes'}
            description={
              <>
                We reserve the right to modify the refund policy without prior notice. Any changes (if any) will be updated on our official website.
                <br />
                <br />
                If you have any questions, please contact us via {' '}
                <a
                  href="mailto:<EMAIL>"
                  className="underline text-[#8F5CFF]"
                ><EMAIL></a>
              </>
            }
          />
        </div>
      </div>
    </LandingPageWrapper>
  );
};
