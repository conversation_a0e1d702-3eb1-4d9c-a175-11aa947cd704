import { LandingPageWrapper } from '@/components/LandingPageWrapper';
import { socialAPI } from '@/api/socialData';
import TablePreview from '@/components/SocialDataDetail/TablePreview';
import { TDataPreview } from '@/utils/SocialData';
import ChartSummarize from '@/components/SocialDataDetail/ChartSummarize';
import { HeaderDetail } from '@/components/SocialDataDetail/HeaderDetail';
import { RiArrowLeftSLine } from '@remixicon/react';
import Link from 'next/link';
import { ResolvingMetadata } from 'next';

interface Props {
  params: Promise<{
    slug: string[],
  }>,
  searchParams: Promise<{[key: string]: string}>
}

export async function generateMetadata({ params }: Props, parent: ResolvingMetadata
) {
  const paramParse = await params;
  if (!paramParse) {
    return {};
  }
  const summarize: any = await handleGetSummarize({ id: paramParse.slug[0] }) as any;
  const parents = await parent;
  const previousKeywords = parents.keywords;
  const previousDescription = parents.description;
  const previousTitle = parents.title;

  return {
    title: `${summarize?.name || 'Dataset library'} - ${previousTitle?.absolute}`,
    description: `${!!summarize?.description ? summarize?.description + ' ' : ''}${previousDescription}`,
    keywords: `${previousKeywords}${!!summarize?.keywords ? '' + summarize?.keywords : ''}`,
    applicationName: 'The Best Social Data Platform In Vietnam'
  };
}

const handleGetAudiences = async ({ id, page }: {id: string, page: string}) => {
  return await socialAPI.get<TDataPreview>({
    endpoint: `audiences/${id}/preview/?page=${page}&limit=10`
  });
};
const handleGetSummarize = async ({ id }: {id: string}) => {
  const res = await socialAPI.get({
    endpoint: `audiences/${id}/`
  });
  return res?.data;
};

const DetailAudience = async ({ ...props }: Props) => {
  const params = await props.params;
  const searchParams = await props.searchParams;
  const page = searchParams.page ?? 1;
  const idSocial = params.slug ? params.slug[0] : '';

  const previewData: TDataPreview = await handleGetAudiences({ id: idSocial, page }) as TDataPreview;
  const summarize: any = await handleGetSummarize({ id: idSocial }) as any;

  return (
    <LandingPageWrapper isHomePage={false} isHideLogo={true}>
      <div className="max-md:p-[16px]">
        <Link
          prefetch={true}
          href={'/dataset-library'}
          className="flex items-center gap-1 md:hidden border-none p-0 m-0 h-auto"
        >
          <RiArrowLeftSLine size={16} color={'#20232C'} />
          <span>Go back</span>
        </Link>
        <HeaderDetail summarize={summarize} />
      </div>
      <div className="md:my-6 max-md:p-[16px]">
        <ChartSummarize loading={false} data={summarize?.summarize || {}} />
      </div>
      <div className="max-md:p-[16px]">
        {previewData && <TablePreview data={previewData} />}
      </div>
    </LandingPageWrapper>
  );
};

export default DetailAudience;
