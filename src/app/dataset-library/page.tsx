import { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { LandingPageWrapper } from '@/components/LandingPageWrapper';
import { socialAPI } from '@/api/socialData';
import { TSocialDataResponse } from '@/utils/constant';
import { TBaseResponse } from '@/utils/ResponseApi';
import { Skeleton } from '@/components/ui/skeleton';

// Lazy load SocialDataView để giảm initial bundle size
const SocialDataView = dynamic(() => import('@/components/SocialDataView'), {
  loading: () => <DatasetLibrarySkeleton />,
  ssr: true // Keep SSR for SEO
});

interface PageProps {
  searchParams: Promise<{[key: string]: string | string[] | undefined}>;
}

// Loading skeleton component
function DatasetLibrarySkeleton() {
  return (
    <div className="max-xl:px-[13px] space-y-6">
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-32" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-32 w-full rounded-lg" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        ))}
      </div>
    </div>
  );
}

export async function generateMetadata() {
  return {
    title: 'Dataset Library - The Best Social Data Platform in Vietnam',
    description: 'Explore our comprehensive dataset library with social audience data',
  };
}

// Optimized data fetching với parallel requests và error handling
async function getListSocial(params: {[key: string]: string | string[] | undefined}) {
  try {
    // Validate và clean params
    const cleanParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) =>
        value !== undefined &&
        value !== '' &&
        value !== null
      )
    );

    const hasValidParams = Object.keys(cleanParams).length > 0;

    // Parallel fetch để giảm thời gian load
    const promises: Promise<any>[] = [
      socialAPI.get<TBaseResponse<TSocialDataResponse>>({
        endpoint: 'audiences-trending/'
      })
    ];

    // Chỉ fetch suggest khi có params
    if (hasValidParams) {
      promises.push(
        socialAPI.get<TBaseResponse<TSocialDataResponse>>({
          endpoint: 'audiences/',
          params: {
            ...cleanParams,
            order_by: cleanParams.order_by ?? '-size',
            limit: cleanParams.limit ?? '20' // Limit để giảm payload
          }
        })
      );
    }

    const results = await Promise.allSettled(promises);

    // Handle results
    const trendingResult = results[0];
    const suggestResult = results[1];

    const trending = trendingResult.status === 'fulfilled'
      ? trendingResult.value?.data
      : { items: [], count: 0 };

    const suggest = hasValidParams && suggestResult?.status === 'fulfilled'
      ? suggestResult.value?.data
      : undefined;

    return {
      trending: trending ?? { items: [], count: 0 },
      suggest
    };
  } catch (error) {
    // Log error nhưng không crash page
    if (process.env.NODE_ENV === 'development') {
      console.error('Error fetching social data:', error);
    }

    // Return safe defaults
    return {
      trending: { items: [], count: 0 },
      suggest: undefined
    };
  }
}

const DatasetLibrary = async ({ searchParams }: PageProps) => {
  const searchParamsProps = await searchParams;

  // Extract và validate search params
  const {
    page,
    q,
    category__in,
    package__in,
    type__in,
    order_by
  } = searchParamsProps;

  // Fetch data với optimized parameters
  const dataSocial = await getListSocial({
    page,
    q,
    category__in,
    package__in,
    type__in,
    order_by
  });

  return (
    <LandingPageWrapper isHomePage={false} isHideLogo={true} isDataset={true}>
      <Suspense fallback={<DatasetLibrarySkeleton />}>
        <div className="max-xl:px-[13px]">
          <SocialDataView data={dataSocial} searchParams={searchParamsProps} />
        </div>
      </Suspense>
    </LandingPageWrapper>
  );
};

export default DatasetLibrary;
