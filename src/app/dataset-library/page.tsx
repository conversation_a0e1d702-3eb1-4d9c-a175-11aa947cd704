import SocialDataView from '@/components/SocialDataView';
import { LandingPageWrapper } from '@/components/LandingPageWrapper';
import { socialAPI } from '@/api/socialData';
import { TSocialDataResponse } from '@/utils/constant';
import { TBaseResponse } from '@/utils/ResponseApi';
import { ResolvingMetadata } from 'next';

interface PageProps {
  searchParams: Promise<{[key: string]: string | string[] | undefined}>;
}

export async function generateMetadata() {
  return {
    title: 'Dataset Library - The Best Social Data Platform in Vietnam',
  };
}

async function getListSocial(params: {[key: string]: string | string[] | undefined}) {
  try {
    const resTrending = await socialAPI.get<TBaseResponse<TSocialDataResponse>>({
      endpoint: 'audiences-trending/'
    });

    // Check if there are any valid parameters
    const hasValidParams = Object.values(params).some(value => value !== undefined);

    // Only fetch suggestions if we have valid parameters
    let resSuggest;
    if (hasValidParams) {
      // Filter out undefined values from params
      const filteredParams = Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== undefined && value !== '')
      );

      resSuggest = await socialAPI.get<TBaseResponse<TSocialDataResponse>>({
        endpoint: 'audiences/',
        params: { ...filteredParams, order_by: filteredParams.order_by ?? '-size' }
      });
    }

    return {
      trending: resTrending?.data ?? {
        items: [],
        count: 0
      },
      suggest: hasValidParams ? resSuggest?.data : undefined
    };
  } catch (error) {
    console.error('Error fetching social data:', error);
    // Return default values in case of error
    return {
      trending: { items: [], count: 0 },
      suggest: undefined
    };
  }
}

const DatasetLibrary = async ({ searchParams }: PageProps) => {
  const searchParamsProps = await searchParams;
  const page = searchParamsProps.page ?? undefined;
  const q = searchParamsProps.q ?? undefined;
  const category__in = searchParamsProps.category__in ?? undefined;
  const package__in = searchParamsProps.package__in ?? undefined;
  const type__in = searchParamsProps.type__in ?? undefined;
  const order_by = searchParamsProps.order_by ?? undefined;

  const dataSocial = await getListSocial({ page, q, category__in, package__in, type__in, order_by });
  return (
    <LandingPageWrapper isHomePage={false} isHideLogo={true} isDataset={true}>
      <div className="max-xl:px-[13px]">
        <SocialDataView data={dataSocial} searchParams={searchParamsProps} />
      </div>
    </LandingPageWrapper>
  );
};
export default DatasetLibrary;
