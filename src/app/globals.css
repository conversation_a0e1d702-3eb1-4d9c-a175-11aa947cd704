@import "tailwindcss";
@plugin "tailwindcss-animate";
@config "../../tailwind.config.js";

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer theme {
  :root {
    /*--primary: #F0F0F0;*/
    --background: 0, 0%, 100%, 1;
    --foreground: 230, 12%, 9%, 1;
    --card: 0, 0%, 100%, 1;
    --card-foreground: 259, 63%, 59%, 1;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;

    --background-primary: 266, 77%, 61%, 1;
    --background-primary-hover: 266, 77%, 50%, 1;
    --background-secondary: 0, 0%, 94%, 1;
    --background-active: 230, 61%, 10%, 0.08;

    --text-primary: 225, 16%, 15%, 1;
    --text-secondary: 226, 12%, 36%, 1;
    --text-tertiary: 226, 8%, 57%, 1;

    --radius: 10px;

    --primary-gradients: 90deg, rgba(112, 81, 240, 0.38) -2.57%, rgba(112, 81, 240, 0) 112.5%;

    --secondary: #515667;
    --secondary-foreground: 220.9 39.3% 11%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;

    --border: 220, 8%, 88%, 1;
    --border-focus: 266, 77%, 74%, 1;
    --ring: 224 71.4% 4.1%;

    --shadow-xs: 0px 1px 2px 0px hsla(230, 13%, 9%, 0.05);
    --shadow-focus: 0px 0px 0px 2px hsla(257, 100%, 85%, 0.5);
    --color-shadow-light: hsl(226, 12%, 36%, 1);
    --color-shadow-medium: hsl(225, 18%, 4%, 0.1);
    --color-shadow-dark: hsl(225, 18%, 4%, 0.15);

    --border-primary: 222, 6%, 67%, 1;
    --border-secondary: 0, 0%, 99%, 1;
    --border-tertiary: 225, 16%, 15%, 1;
    --border-inverse: 0, 0%, 94%, 1;
    --border-disable: 210, 3%, 89%, 1;

    --dp-2xl: 4.5rem;
    --dp-2xl-line-height: 5.625rem;
    --dp-2xl-letter-spacing: -2%;

    --dp-xl: 3.75rem;
    --dp-xl-line-height: 4.5rem;
    --dp-xl-letter-spacing: -2%;

    --dp-lg: 3rem;
    --dp-lg-line-height: 3.75rem;
    --dp-lg-letter-spacing: -2%;

    --dp-md: 2.25rem;
    --dp-md-line-height: 2.75rem;
    --dp-md-letter-spacing: -2%;

    --dp-sm: 1.875rem;
    --dp-sm-line-height: 2.375rem;

    --dp-xs: 1.5rem;
    --dp-xs-line-height: 2rem;

    --text-xl: 1.25rem;
    --text-xl-line-height: 1.875rem;

    --text-lg: 1.125rem;
    --text-lg-line-height: 1.75rem;

    --text-md: 1rem;
    --text-md-line-height: 1.5rem;

    --text-sm: 0.875rem;
    --text-sm-line-height: 1.25rem;

    --text-xs: 0.75rem;
    --text-xs-line-height: 1.125rem;
  }
}

html {
  height: 100vh;
}
body {
  font-family: "Inter", sans-serif;
  font-weight: 400;
  box-sizing: border-box;
  height: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-smooth: never;
}
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  /* background: red; */
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 5px;
}
*:hover::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.scroll-bottom::-webkit-scrollbar-thumb {
  background: #888 !important;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  text-indent: 1px;
  text-overflow: "";
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input:-webkit-autofill {
  -webkit-background-clip: text;
}
.swiper-remove-time-transition > .swiper-wrapper {
  -webkit-transition-timing-function: linear;
  -o-transition-timing-function: linear;
  transition-timing-function: linear !important;
}

.ag-root-wrapper, .ag-header {
  border: none !important;
}
.ag-center-cols-viewport{
  padding: 0 !important;
}

.custom--dropdown-container {
  text-align: left;
  border: 1px solid #dee0e3;
  position: relative;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #6b7183;
  box-sizing: content-box;
}

.custom--dropdown-container .dropdown-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;
  gap: 6px;
  padding: 8px;
  border-radius: 12px;
}

.custom--dropdown-container .dropdown-input .dropdown-selected-value.placeholder {
  color: #6b7183;
}

.custom--dropdown-container .dropdown-tool svg {
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  transform: rotate(0deg);
}

.custom--dropdown-container .dropdown-tool svg.translate {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.custom--dropdown-container .dropdown-menu {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: 100%;
  padding: 12px 8px;
  position: absolute;
  border: 1px solid #e1e2e3;
  border-radius: 12px;
  overflow: auto;
  background-color: #fff;
  z-index: 99;
  max-height: 312px;
  min-height: 50px;
  left: 0;
  margin-top: 4px;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar {
  width: 5px;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar-track {
  background: #fdfdfd;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar-thumb {
  background: #888;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.custom--dropdown-container .dropdown-menu.alignment--left {
  left: 0;
}

.custom--dropdown-container .dropdown-menu.alignment--right {
  right: 0;
}

.custom--dropdown-container .dropdown-item {
  padding: 8px;
  cursor: pointer;
  color: #515667;
  -webkit-transition: background-color 0.35s ease;
  transition: background-color 0.35s ease;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  margin: 8px 0;
}

.custom--dropdown-container .dropdown-item:hover {
  background-color: #ecdffb;
  color: #5314a3;
}

.custom--dropdown-container .dropdown-item.selected {
  background-color: #ecdffb;
  color: #5314a3;
  font-weight: 600;
}

.custom--dropdown-container .search-box {
  padding: 8px;
}

.custom--dropdown-container .search-box input {
  width: 100%;
}

.custom--dropdown-container .dropdown-tags {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 5px;
}

.custom--dropdown-container .dropdown-tag-item {
  background-color: #ecdffb;
  color: #5314a3;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 4px;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  z-index: 3;
}

.custom--dropdown-container .dropdown-tag-close {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-left: 5px;
}

/*pagination*/
.pagination-container {
  display: flex;
  list-style-type: none;
  justify-content: center;

  .pagination-item {
    padding: 0 12px;
    height: 32px;
    text-align: center;
    margin: auto 4px;
    color: rgba(15, 19, 36, 0.6);
    display: flex;
    box-sizing: border-box;
    align-items: center;
    letter-spacing: 0.01071em;
    border-radius: 8px;
    line-height: 1.43;
    font-size: 13px;
    min-width: 32px;

    &.dots:hover {
      background-color: transparent;
      cursor: default;
    }
    &:hover {
      background-color: rgba(10, 15, 41, 0.04);
      color: rgba(20, 21, 26, 1);
      cursor: pointer;
    }

    &.selected {
      background-color: rgba(10, 15, 41, 0.06);
      color: rgba(20, 21, 26, 1);
    }

    .arrow {
      &::before {
        position: relative;
        /* top: 3pt; Uncomment this to lower the icons as requested in comments*/
        content: "";
        /* By using an em scale, the arrows will size with the font */
        display: inline-block;
        width: 0.4em;
        height: 0.4em;
        border-right: 0.12em solid rgba(0, 0, 0, 0.87);
        border-top: 0.12em solid rgba(0, 0, 0, 0.87);
      }

      &.left {
        transform: rotate(-135deg) translate(-50%);
      }

      &.right {
        transform: rotate(45deg);
      }
    }

    &.disabled {
      pointer-events: none;

      .arrow::before {
        border-right: 0.12em solid rgba(0, 0, 0, 0.43);
        border-top: 0.12em solid rgba(0, 0, 0, 0.43);
      }

      &:hover {
        background-color: transparent;
        cursor: default;
      }
    }
  }
}
/*chart*/
.chartjs-tooltip {
  background: white;
  border-radius: 6px;
  pointer-events: none;
  position: absolute;
  transform: translate(-20%, calc(-100% - 20px));
  border: 1px solid hsla(230, 13%, 9%, 0.1);
  box-shadow: 0px 3px 10px -2px hsla(230, 13%, 9%, 0.02),
  0px 10px 16px -3px hsla(230, 13%, 9%, 0.05);
  transition: all 0.2s ease;
}
.chartjs-tooltip-header {
  color: hsla(230, 12%, 9%, 1);
  font-size: 14px;
  font-weight: 500;
  background-color: hsla(240, 8%, 97%, 1);
  padding: 3.5px 12px 3.5px 12px;
}

.chartjs-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 12%;
  border-width: 10px;
  border-style: solid;
  border-color: white transparent transparent transparent;
}

.dots-tooltip {
  height: 10px;
  width: 10px;
  display: inline-block;
  border-radius: 50%;
  flex-shrink: 0;
}
.table-body-tooltips {
  display: flex;
  flex-direction: column;
}

.value-tooltips {
  display: flex;
  align-items: center;
  gap: 6px;
}
