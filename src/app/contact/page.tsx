import { LandingPageWrapper } from '@/components/LandingPageWrapper';
import React from 'react';
import { BlockContentLanding } from '@/components/BlockContentLanding';
import { Box } from '@/components/Box';
import { RiMailLine, RiMapPinLine, RiPhoneLine } from '@remixicon/react';
import { HeaderPage } from '@/components/HeaderPage';

export async function generateMetadata() {
  return {
    title: 'Contact - The Best Social Data Platform in Vietnam',
  };
}

const Contact = () => {
  return (
    <LandingPageWrapper containerClassName="mb-[40px]" mainClassName='xl:fle-1' className="xl:flex xl:flex-1 xl:flex-col">
      <div className="text-center max-w-[1156px] mx-auto mb-10">
        <HeaderPage
          title={<>We’re excited to help and<br />
            eager to hear from you</>}
        />
      </div>
      <div className="max-w-[448px] m-auto">
        <BlockContentLanding
          title={'Contacts information'}
          classTitle={'text-[18px]'}
          className={'justify-end items-start p-10 gap-3'}
          description={<>
            <div className="text-primary text-base">
              <Box variant="col-start" className="gap-2 flex-1  gap-3">
                <Box className="gap-2 justify-start text-secondary font-medium text-sm">
                  <RiMapPinLine size={16} />
                  <span>1001 S. Main St. STE 700, Kalispell, MT 59901</span>
                </Box>
                <a href="tel:+14063087751">
                  <Box className="gap-2 justify-start text-secondary font-medium text-sm">
                    <RiPhoneLine size={16} />
                    <span>+14063087751</span>
                  </Box>
                </a>
                <a href="mailto:<EMAIL>">
                  <Box className="gap-2 justify-start text-secondary font-medium text-sm">
                    <RiMailLine size={16} />
                    <EMAIL>
                  </Box>
                </a>
              </Box>
            </div>
          </>}
        />
      </div>
    </LandingPageWrapper>
  );
};

export default Contact;
