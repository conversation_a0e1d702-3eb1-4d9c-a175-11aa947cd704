import type { <PERSON>ada<PERSON>, Viewport } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from 'next/font/google';
import './globals.css';
import { socialAPI } from '@/api/socialData';
import { TBaseResponse } from '@/utils/ResponseApi';
import { DimCities, TCategoriesResponse } from '@/utils/constant';
import { SWRConfig } from 'swr';
import Script from 'next/script';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Big360 - The Best Social Data Platform in Vietnam",
  description: "Leverage Data – Expand Opportunities – Achieve Sustainable Growth",
  keywords: [
    "Social Data", "Audience Data", "Social Media", "Data Platform", "Vietnam",
    "social data analysis", "social media data mining", "Facebook group insights",
    "fanpage analytics", "user persona segmentation", "customer profiling tool"
  ],
  applicationName: "big360.ai",
  authors: [{ name: "Big360 Inc.", url: "https://big360.ai" }],
  generator: "Big360 Inc.",
  creator: "Big360 Inc.",
  publisher: "Big360 Inc.",
  metadataBase: new URL("https://big360.ai"),
  openGraph: {
    title: "The Best Social Data Platform in Vietnam",
    description: "Leverage Data – Expand Opportunities – Achieve Sustainable Growth",
    url: "https://big360.ai",
    siteName: "big360.ai",
    images: [
      {
        url: "https://big360.ai/og-image.png",
        width: 1200,
        height: 627,
        alt: "The Best Social Data Platform in Vietnam",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  icons: {
    icon: [
      { url: "/favicon.ico", type: "image/x-icon" },
      { url: "/favico.svg", type: "image/svg+xml" },
    ],
    shortcut: "/favicon.ico",
    apple: "/favicon-180.png",
  },
  category: "Business, Technology, Data Analytics, Data Science, Data Platform, Data Mining, Data Visualization, Data Management, Business Intelligence, Social Data, Artificial Intelligence",
  manifest: "/site.webmanifest",
};

async function getCategories() {
  const res = await socialAPI.get<TBaseResponse<TCategoriesResponse>>({
    endpoint: 'categories/'
  });
  return res?.data as TCategoriesResponse;
}

async function getAllCities() {
  return await socialAPI.getDims('city');
}

// custom viewport
// disable auto-scale up webview in IOS
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const categories: TCategoriesResponse = await getCategories();
  const cities: DimCities = await getAllCities();
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
      <SWRConfig value={{ fallback: { 'categories': categories, 'cities': cities } }}>
        {children}
      </SWRConfig>
      <Script id="application" type="application/ld+json">
        {`
          {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Big360 Inc.",
            "url": "https://big360.ai",
            "logo": "https://big360.ai/og-image.png",
            "sameAs": [
              "https://www.facebook.com/big360ai",
              "https://www.linkedin.com/company/big360ai"
            ],
            "description": "Leverage Data – Expand Opportunities – Achieve Sustainable Growth",
            "keywords": [
              "Business", "Technology", "Data Analytics", "Social Media",
              "Data Platform", "Vietnam", "Artificial Intelligence"
            ]
          }
        `}
      </Script>
        <Script id="gtag-manager" async src="https://www.googletagmanager.com/gtag/js?id=G-DKX6VTZXK3" />
        <Script id="gtag" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-DKX6VTZXK3');
          `}
        </Script>
        <Script id="hotjar" strategy="afterInteractive">
          {`
            (function(h,o,t,j,a,r){
              h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
              h._hjSettings={hjid:5364475,hjsv:6};
              a=o.getElementsByTagName('head')[0];
              r=o.createElement('script');r.async=1;
              r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
              a.appendChild(r);
            })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
          `}
        </Script>
      </body>
    </html>
  );
}
