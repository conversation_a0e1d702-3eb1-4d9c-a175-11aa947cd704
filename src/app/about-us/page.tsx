import Link from 'next/link';
import { LandingPageWrapper } from '@/components/LandingPageWrapper';
import { Box } from '@/components/Box';
import Image from 'next/image';
import { RiLineChartLine, RiRefreshLine, RiUserSearchLine } from '@remixicon/react';
import { APP_DOMAIN } from '@/utils/constant';
import { ResolvingMetadata } from 'next';
import { HeaderPage, HeaderSecondary } from '@/components/HeaderPage';

const valueArr = [
  {
    title: 'Customer Satisfaction',
    description: 'We put our customers first and aim to exceed their expectations.'
  },
  {
    title: 'Integrity',
    description: 'We conduct our business with honesty and transparency.'
  },
  {
    title: 'Innovation',
    description: 'We continuously seek new ways to improve our products and services.'
  },
  {
    title: 'Sustainability',
    description: 'We are committed to environmentally responsible practices.'
  }
];

const arrWWD = [
  {
    icon: <RiUserSearchLine size={25} color={'#5A18BF'}/>,
    title: 'Find the Right Information Quickly',
    description: 'Whether you are a seller looking for potential customers, a recruiter seeking top talents, or a marketer analyzing trends, our platform enables precise and efficient searches.'
  },
  {
    icon: <RiLineChartLine size={25} color={'#5A18BF'}/>,
    title: 'Optimize Performance',
    description: 'Our smart algorithms process vast amounts of data to help businesses make strategic decisions with confidence.'
  },
  {
    icon: <RiRefreshLine size={25} color={'#5A18BF'}/>,
    title: 'Enhance Competitiveness',
    description: 'With real-time analytics and deep insights, users gain a competitive edge in their industry.'
  }
];

export async function generateMetadata() {
  return {
    title: 'About us - The Best Social Data Platform in Vietnam',
  };
}

export default function AboutUsPage  () {
  return (
    <LandingPageWrapper>
      <div className="text-center max-w-[1156px] mx-auto">
        <HeaderPage
          title={<>About Us</>}
        />
        <HeaderSecondary title={<>Who we are</>}/>
        <p className="text-secondary text-base font-normal leading-6">
          The website www.big360.ai is owned and operated by Big360 Inc. As a state-of-the-art data integration platform,<br />
          Big360.ai is designed to empower sellers, recruiters, and marketers with the power of big data. Our mission is to<br />
          revolutionize data-driven decision-making by offering an intuitive, intelligent solution that enhances search<br />
          capabilities, streamlines operations, and delivers actionable insights with precision and efficiency.
        </p>
        <div className="my-10">
          <Link href={APP_DOMAIN} target={'_blank'} className="bg-[#8F5CFF] text-sm font-medium h-10 p-3 text-[#FDFDFD] rounded-xl">
            Get started
          </Link>
        </div>
        <div>
          <HeaderSecondary title={<>What we do</>}/>
          Big360.ai leverages advanced AI and big data technology to help businesses<br />
          <div className="pt-[24px]">
            <Box className='max-lg:flex-wrap justify-center lg:justify-between'>
              {arrWWD.map((item, index: number) =>
                <div
                  className='max-w-[364px] min-h-[236px] text-left rounded-2xl p-4' key={index}
                  style={{
                    boxShadow: '0px 0px 32px 0px rgba(9, 10, 13, 0.02), 0px 4px 20px -8px rgba(9, 10, 13, 0.10)'
                  }}
                >
                  <div className="w-10 h-10 rounded-xl bg-[#E2DAFF] flex items-center justify-center mb-4">
                    {item.icon}
                  </div>
                  <h3 className="text-primary font-medium text-lg">
                    {item.title}
                  </h3>
                  <p className="text-secondary text-base">
                    {item.description}
                  </p>
                </div>
              )}
            </Box>

          </div>

        </div>
        <div className="mt-10 mb-6 p-10 text-center">
          <HeaderSecondary title={<>Why Big360.ai</>}/>
          <div className="mt-8 tracking-[0.2px] text-sm">
            <p className="mb-6">
              <span>&#x2022;&ensp;</span>Powerful Data Integration – We aggregate and analyze large-scale data from multiple sources to provide meaningful and structured insights.
            </p>
            <p className="mb-6">
              <span>&#x2022;&ensp;</span>User-Friendly Interface – Designed for ease of use, Big360.ai ensures that even non-technical users can navigate and extract valuable information effortlessly.
            </p>
            <p>
              <span>&#x2022;&ensp;</span>Scalable Solutions – Our platform adapts to businesses of all sizes, from startups to large enterprises, offering customized solutions tailored to specific needs
            </p>
          </div>
        </div>
        <div className="relative md:h-[530px] lg:max-h-[702px]">
          <div
            className="rounded-2xl w-full h-[320px] sm:h-[400px] lg:h-[422px]"
            style={{
              background: 'radial-gradient(197.48% 152.51% at 2.6% 12.86%, #E2DAFF 14%, #FFF 89%)'
            }}
          />
          <Image
            className="absolute top-0 left-1/2 transform transform translate-x-[-50%] translate-y-[65px] mx-auto rounded-2xl"
            src={'/imageBanner.webp'}
            alt={'imageBanner'}
            width={980}
            height={542}
            quality={100}
          />
        </div>
        <div className="mt-10 min-lg:mt-25 min-[700px]:mt-20 min-[600px]:mt-25 min-[500px]:mt-20 pb-4 text-center">
          <HeaderSecondary title={<> Our Value</>}/>
          <p className="text-sm text-secondary">
            We are guided by the following core values:
          </p>
          <Box className="gap-6 items-stretch mt-12 flex-col md:flex-row">
            {valueArr.map((item, index) => (
              <div key={index} className="p-6 max-md:max-w-full max-w-[212px] border rounded-2xl shadow-sm">
                <p className="text-lg text-primary mb-[16px]">
                  {item.title}
                </p>
                <p className="text-sm text-secondary">
                  {item.description}
                </p>
              </div>
            ))}
          </Box>
        </div>
      </div>
    </LandingPageWrapper>
  );
};
