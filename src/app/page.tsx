import { LandingPageWrapper } from '@/components/LandingPageWrapper';
import Image from 'next/image';
import { Box } from '@/components/Box';
import React from 'react';
import { Features } from '@/components/Features';
import { EffectTyping } from '@/components/EffectTyping';
import { OurMassive } from '@/components/OurMassive';
import { AboutUsLanding } from '@/components/AboutUsLanding';
import { SwiperContainer } from '@/components/SwiperContainer';
import { Partners } from '@/components/Partners';
import Link from 'next/link';
import { APP_DOMAIN } from '@/utils/constant';

export default function Home() {
  return (
    <LandingPageWrapper isHomePage={true}>
      <div
        className="md:mx-6 mt-[24px] lg:mt-12 mb-[24px] lg:mb-12 lg:rounded-2xl relative h-[calc(100vh-160px)]"
      >
        <div
          className="absolute inset-0 lg:rounded-2xl z-10"
          style={{
            background: `linear-gradient(247deg, rgba(32, 35, 44, 0.20) 8.29%, #090A0D 118.37%), linear-gradient(0deg, rgba(32, 35, 44, 0.20) 0%, rgba(32, 35, 44, 0.20) 100%)`,
            backgroundBlendMode: 'darken, normal, normal'
          }}
        />
        <Image
          src={'/imageBannerHome2.png'}
          loading="lazy"
          quality={100}
          width={0}
          height={0}
          sizes="100vw"
          className="lg:rounded-2xl bg-blend-darken object-cover absolute w-full h-full"
          alt={'imageBannerHome2'}
        />
        <div className="absolute w-10/12 z-10 top-1/2 left-0 transform translate-x-[24px] lg:translate-x-[48px] -translate-y-1/2 text-primary">
          <h1 className="text-[#FDFDFD] font-medium text-[36px] lg:text-[72px] leading-10 lg:leading-[84px]">
            The Ultimate Tool
            <br />
            for <EffectTyping />
          </h1>
          <h3 className="text-[#FDFDFD] text-[14px] lg:text-[24px] font-medium min-w-[327px]">
            Leverage Data – Expand Opportunities – Achieve Sustainable Growth
          </h3>
          <Box className="mt-[64px] gap-[16px] justify-start">
            <Link
              href={APP_DOMAIN}
              target={'_blank'}
              className="inline-flex items-center justify-center whitespace-nowrap ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary rounded-[10px] border hover:bg-primary-hover h-10 px-4 py-2 border-[#914fe8] font-medium text-[#FDFDFD] text-[14px] cursor-pointer"
            >
              Get started
            </Link>
            <Link
              prefetch={true}
              href={'/dataset-library'}
              className="h-10 flex items-center justify-center px-[16px] py-[8px] rounded-[10px] border border-[#A7AAB1] bg-transparent font-medium text-[#FDFDFD] text-[14px] cursor-pointer"
            >
              Dataset Library
            </Link>
          </Box>
        </div>
      </div>
      <div className="px-[16px]">
        <Box className="gap-8 max-w-[1156] mx-auto items-start flex-col">
          <p className="font-semibold text-sm md:text-md lg:text-xl text-[#000] uppercase">
            Join over 1,000 businesses using Big360.ai
          </p>
          <Partners />
        </Box>
        <SwiperContainer />
        <Features />
      </div>
      <OurMassive />
      <div className="px-[16px]">
        <AboutUsLanding />
      </div>
    </LandingPageWrapper>
  );
}
