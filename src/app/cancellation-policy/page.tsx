import { BlockContentLanding } from '@/components/BlockContentLanding';
import { LandingPageWrapper } from '@/components/LandingPageWrapper';
import { RuleContent } from '@/components/RuleContent';
import React from 'react';
import { HeaderPage } from '@/components/HeaderPage';

export async function generateMetadata() {
  return {
    title: 'Cancellation policy - The Best Social Data Platform in Vietnam',
  };
}


export default function CancellationPolicyPage() {
  return (
    <LandingPageWrapper>
      <div className="text-center max-w-[1156px] mx-auto">
        <HeaderPage
          title={<>Cancellation policy</>}
        />
        <p className="mb-[40px]">
          <strong>Last Updated:</strong> March 3, 2025
        </p>
        <BlockContentLanding
          description={<>
            <p className="text-primary text-base mb-[24px]">
              At <strong>Big360 Inc.</strong> , we understand that plans may change. This Cancellation Policy outlines the conditions under which cancellations are accepted and any applicable fees.
            </p>
          </>}
        />
      </div>
      <div className="max-w-[1156px] mx-auto mt-[40px]">
        <RuleContent
          title={'1. Cancellation Eligibility'}
          description={
            <>
              Cancellations may be requested under the following conditions:
              <br />
              <br />
              <ul className="list-disc pl-6">
                <li>
                  The request is made within 2 days of the purchase or booking.
                </li>
                <li>
                  The service has not yet been rendered.
                </li>
                <li>
                  Subscription services must be canceled before the next billing cycle to avoid charges.
                </li>
              </ul>
            </>
          }
        />
        <RuleContent
          title={'2. Non-Cancellable Items'}
          description={
            <>
              Certain products or services are non-cancellable, including:
              <br />
              <br />
              <ul className="list-decimal pl-6">
                <li>
                  Digital products that have been accessed or downloaded.
                </li>
                <li>
                  Customized or personalized items.
                </li>
                <li>
                  Services that have already been provided.
                </li>
              </ul>
            </>
          }
        />
        <RuleContent
          title={'3. Cancellation Process'}
          description={
            <>
              <ul className="list-disc pl-6">
                <li>
                  To request a cancellation, follow these steps:
                </li>
              </ul>
              <br />
              <ol className="list-[lower-alpha] pl-12">
                <li>
                  Contact our support team at{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="underline text-[#8F5CFF]"
                  >
                    <EMAIL>
                  </a> with your order details.
                </li>
                <li>
                  Provide a reason for cancellation and any necessary supporting documentation.
                </li>
                <li>
                  We will review your request and notify you of the approval or rejection.
                </li>
              </ol>
            </>
          }
        />
        <RuleContent
          title={'4. Refunds for Cancellations'}
          description={
            <>
              <ul className="list-disc pl-6">
                <li>
                  Approved cancellations may be eligible for a full or partial refund based on the nature of the product or service.

                </li>
                <li>
                  Refunds, if applicable, will be processed within 5 business days to the original payment method.
                </li>
              </ul>
            </>
          }
        />
        <RuleContent
          title={'5. Changes to This Policy'}
          description={'We reserve the right to update this Cancellation Policy at any time. Any changes will be posted on our website.'}
        />
        <div className="mb-[40px]">
          <RuleContent
            title={'6. Contact Us'}
            description={
              <>
                For any questions regarding our Cancellation Policy, please contact us at{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="underline text-[#8F5CFF]"
                >
                  <EMAIL>.
                </a>
              </>
            }
          />
        </div>
      </div>
    </LandingPageWrapper>
  );
};
